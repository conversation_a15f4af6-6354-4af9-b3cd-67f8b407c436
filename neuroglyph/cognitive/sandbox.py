"""
NEUROGLYPH Sandbox
Ambiente di esecuzione sicura per codice Python
"""

import sys
import time
import signal
import threading
from io import String<PERSON>
from contextlib import redirect_stdout, redirect_stderr
from typing import Optional, Dict, Any

from .sandbox_structures import (
    ExecutionContext, ExecutionResult, ExecutionStatus, ExecutionMode,
    SecurityLevel, ResourceLimits, SandboxConfig
)
from .security_monitor import SecurityMonitor


class TimeoutException(Exception):
    """Eccezione per timeout di esecuzione."""
    pass


class NGSandbox:
    """
    Sandbox sicuro per l'esecuzione di codice Python.
    Fornisce isolamento, monitoring delle risorse e controlli di sicurezza.
    """
    
    def __init__(self, config: Optional[SandboxConfig] = None):
        """
        Inizializza il sandbox.
        
        Args:
            config: Configurazione sandbox
        """
        self.config = config or SandboxConfig()
        self.security_monitor = SecurityMonitor(self.config)
        self.active_executions = {}
        
        print("🏖️ NGSandbox inizializzato")
        print(f"   - Default timeout: {self.config.default_timeout}s")
        print(f"   - Default memory limit: {self.config.default_memory_limit}MB")
        print(f"   - Security level: {self.config.default_security_level}")
        print(f"   - Execution logging: {self.config.enable_execution_logging}")
    
    def execute(self, context: ExecutionContext) -> ExecutionResult:
        """
        Esegue codice nel sandbox.
        
        Args:
            context: Contesto di esecuzione
            
        Returns:
            Risultato dell'esecuzione
        """
        result = ExecutionResult(execution_context=context)
        
        try:
            # Pre-execution security check
            violations = self.security_monitor.analyze_code_security(
                context.code, context.security_level
            )
            
            if violations:
                critical_violations = [v for v in violations if v.severity == "critical"]
                if critical_violations:
                    result.status = ExecutionStatus.BLOCKED
                    result.error_type = "security_violation"
                    result.error_message = f"Critical security violations: {len(critical_violations)}"
                    result.metadata["violations"] = [
                        {"type": v.violation_type, "message": v.message} 
                        for v in critical_violations
                    ]
                    result.finalize()
                    return result
            
            # Setup execution environment
            safe_globals = self._create_safe_environment(context)
            safe_locals = context.locals_dict.copy()
            
            # Execute with monitoring
            with self.security_monitor.monitor_execution(context.resource_limits):
                result = self._execute_with_timeout(context, safe_globals, safe_locals, result)
            
            # Post-execution cleanup
            result.resource_usage = self.security_monitor.resource_usage
            
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_type = type(e).__name__
            result.error_message = str(e)
            import traceback
            result.traceback = traceback.format_exc()
        
        result.finalize()
        
        if self.config.enable_execution_logging:
            self._log_execution(result)
        
        return result
    
    def _create_safe_environment(self, context: ExecutionContext) -> Dict[str, Any]:
        """Crea ambiente di esecuzione sicuro."""
        # Inizia con globals sicuri dal security monitor
        safe_globals = self.security_monitor.create_safe_globals(context.security_level)
        
        # Aggiungi globals personalizzati dal context
        for key, value in context.globals_dict.items():
            if self._is_safe_value(key, value, context.security_level):
                safe_globals[key] = value
        
        # Aggiungi funzioni di utilità sicure
        safe_globals.update({
            '__name__': '__sandbox__',
            '__doc__': None,
            '__package__': None,
        })
        
        return safe_globals
    
    def _is_safe_value(self, key: str, value: Any, security_level: SecurityLevel) -> bool:
        """Verifica se un valore è sicuro da includere nell'ambiente."""
        # Blocca nomi pericolosi
        dangerous_names = ['__builtins__', '__import__', 'exec', 'eval', 'open']
        if key in dangerous_names:
            return False
        
        # Blocca moduli pericolosi
        if hasattr(value, '__name__') and hasattr(value, '__file__'):
            module_name = getattr(value, '__name__', '')
            if module_name in self.config.blocked_modules:
                return False
        
        # Blocca funzioni pericolose
        if callable(value):
            func_name = getattr(value, '__name__', '')
            if func_name in self.config.blocked_builtins:
                return False
        
        return True
    
    def _execute_with_timeout(self, context: ExecutionContext, safe_globals: Dict[str, Any], 
                             safe_locals: Dict[str, Any], result: ExecutionResult) -> ExecutionResult:
        """Esegue codice con timeout."""
        
        # Setup timeout
        timeout = min(context.resource_limits.max_execution_time, self.config.max_timeout)
        
        # Capture output
        stdout_capture = StringIO()
        stderr_capture = StringIO()
        
        def timeout_handler(signum, frame):
            raise TimeoutException(f"Execution timeout after {timeout}s")
        
        # Setup signal handler (solo su Unix)
        old_handler = None
        if hasattr(signal, 'SIGALRM'):
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(timeout))
        
        try:
            result.status = ExecutionStatus.RUNNING
            
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                if context.mode == ExecutionMode.EVAL:
                    # Valuta espressione
                    result.result = eval(context.code, safe_globals, safe_locals)
                elif context.mode == ExecutionMode.EXEC:
                    # Esegui statement
                    exec(context.code, safe_globals, safe_locals)
                    result.result = safe_locals.get('result', None)
                elif context.mode == ExecutionMode.COMPILE:
                    # Solo compila
                    result.result = compile(context.code, '<sandbox>', 'exec')
                elif context.mode == ExecutionMode.VALIDATE:
                    # Solo valida sintassi
                    compile(context.code, '<sandbox>', 'exec')
                    result.result = True
            
            result.status = ExecutionStatus.COMPLETED
            
        except TimeoutException:
            result.status = ExecutionStatus.TIMEOUT
            result.error_type = "TimeoutException"
            result.error_message = f"Execution timed out after {timeout}s"
            
        except SyntaxError as e:
            result.status = ExecutionStatus.FAILED
            result.error_type = "SyntaxError"
            result.error_message = str(e)
            result.metadata["line_number"] = e.lineno
            result.metadata["text"] = e.text
            
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_type = type(e).__name__
            result.error_message = str(e)
            import traceback
            result.traceback = traceback.format_exc()
            
        finally:
            # Cleanup timeout
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)
                if old_handler:
                    signal.signal(signal.SIGALRM, old_handler)
            
            # Capture output
            result.stdout = stdout_capture.getvalue()
            result.stderr = stderr_capture.getvalue()
            
            # Update resource usage
            result.resource_usage.stdout_size = len(result.stdout)
            result.resource_usage.stderr_size = len(result.stderr)
        
        return result
    
    def execute_code(self, code: str, timeout: Optional[float] = None, 
                    security_level: Optional[SecurityLevel] = None,
                    globals_dict: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        Metodo semplificato per eseguire codice.
        
        Args:
            code: Codice da eseguire
            timeout: Timeout in secondi
            security_level: Livello di sicurezza
            globals_dict: Variabili globali
            
        Returns:
            Risultato dell'esecuzione
        """
        # Crea contesto
        context = ExecutionContext(
            code=code,
            mode=ExecutionMode.EXEC,
            security_level=security_level or self.config.default_security_level,
            globals_dict=globals_dict or {}
        )
        
        # Imposta timeout
        if timeout:
            context.resource_limits.max_execution_time = min(timeout, self.config.max_timeout)
        
        return self.execute(context)
    
    def evaluate_expression(self, expression: str, timeout: Optional[float] = None,
                           globals_dict: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        Valuta un'espressione Python.
        
        Args:
            expression: Espressione da valutare
            timeout: Timeout in secondi
            globals_dict: Variabili globali
            
        Returns:
            Risultato della valutazione
        """
        context = ExecutionContext(
            code=expression,
            mode=ExecutionMode.EVAL,
            security_level=self.config.default_security_level,
            globals_dict=globals_dict or {}
        )
        
        if timeout:
            context.resource_limits.max_execution_time = min(timeout, self.config.max_timeout)
        
        return self.execute(context)
    
    def validate_code(self, code: str) -> ExecutionResult:
        """
        Valida sintassi del codice senza eseguirlo.
        
        Args:
            code: Codice da validare
            
        Returns:
            Risultato della validazione
        """
        context = ExecutionContext(
            code=code,
            mode=ExecutionMode.VALIDATE,
            security_level=SecurityLevel.MINIMAL
        )
        
        return self.execute(context)
    
    def _log_execution(self, result: ExecutionResult):
        """Log dell'esecuzione."""
        if not self.config.enable_execution_logging:
            return
        
        status_emoji = {
            ExecutionStatus.COMPLETED: "✅",
            ExecutionStatus.FAILED: "❌", 
            ExecutionStatus.TIMEOUT: "⏰",
            ExecutionStatus.BLOCKED: "🚫"
        }.get(result.status, "❓")
        
        print(f"🏖️ Sandbox execution {status_emoji}")
        print(f"   - Status: {result.status.value}")
        print(f"   - Duration: {result.duration:.3f}s")
        print(f"   - Memory: {result.resource_usage.peak_memory_mb:.1f}MB")
        
        if result.error_message:
            print(f"   - Error: {result.error_type}: {result.error_message}")
        
        if result.stdout:
            lines = result.stdout.split('\n')[:3]  # Prime 3 linee
            print(f"   - Output: {lines[0][:50]}...")
    
    def get_security_violations(self) -> list:
        """Ottiene violazioni di sicurezza rilevate."""
        return self.security_monitor.get_violations()
    
    def clear_security_violations(self):
        """Pulisce violazioni di sicurezza."""
        self.security_monitor.clear_violations()
    
    def is_code_safe(self, code: str, security_level: Optional[SecurityLevel] = None) -> bool:
        """
        Verifica se il codice è sicuro da eseguire.
        
        Args:
            code: Codice da verificare
            security_level: Livello di sicurezza
            
        Returns:
            True se sicuro
        """
        level = security_level or self.config.default_security_level
        violations = self.security_monitor.analyze_code_security(code, level)
        critical_violations = [v for v in violations if v.severity == "critical"]
        return len(critical_violations) == 0
