"""
NEUROGLYPH Cognitive Modules
Moduli per il reasoning simbolico e l'intelligenza cognitiva
"""

from .context_prioritizer import NGContextPrioritizer
from .memory import NGMemory
from .reasoner import NGReasoner

# TODO: Implementare altri moduli cognitivi
# from .self_check import NGSelfCheck
# from .sandbox import NGSandbox
# from .adaptive_patcher import NGAdaptivePatcher
# from .learner import NGLearner

__all__ = [
    'NGContextPrioritizer',
    'NGMemory',
    'NGReasoner',
    # 'NGSelfCheck',
    # 'NGSandbox',
    # 'NGAdaptivePatcher',
    # 'NGLearner'
]
