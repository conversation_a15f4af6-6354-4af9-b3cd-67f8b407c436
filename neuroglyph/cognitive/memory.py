"""
NEUROGLYPH Memory System
Sistema di memoria a lungo termine con SQLite + FTS5/RTREE
"""

import sqlite3
import json
import time
import math
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from uuid import UUID, uuid4
from pathlib import Path

import numpy as np
from scipy.spatial.distance import cosine

from .memory_models import (
    MemoryRecord, MemorySearchQuery, MemorySearchResult, 
    MemoryStats, MemoryConfig
)
from .data_structures import PriorityVector, MemoryContext


class NGMemory:
    """
    Sistema di memoria a lungo termine per NEUROGLYPH.
    
    Features:
    - Append: Salva MemoryRecord con ≤0.5ms
    - Search: KNN su PriorityVector con ≤5ms per 1k records
    - Decay: Score esponenziale con reuse bonus
    - Evict: Policy LRU+low-score con hard cap
    - Snapshot: Dump binario compresso
    """
    
    def __init__(self, config: Optional[MemoryConfig] = None):
        """
        Inizializza NGMemory.
        
        Args:
            config: Configurazione memoria (default: MemoryConfig())
        """
        self.config = config or MemoryConfig()
        self.db_path = self.config.db_path
        self.lock = threading.RLock()

        # Per database in memoria, usa connessione persistente
        if self.db_path == ":memory:":
            self._conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self._init_database_direct(self._conn)
        else:
            self._conn = None
            self._init_database()
        
        # Cache per performance
        self._prio_vec_cache = {}
        self._last_decay_update = time.time()
        self._last_vacuum = time.time()
        
        print(f"🧠 NGMemory inizializzato")
        print(f"   - Database: {self.db_path}")
        print(f"   - Max records: {self.config.max_records:,}")
        print(f"   - Decay lambda: {self.config.decay_lambda}")
        print(f"   - FTS enabled: {self.config.enable_fts}")
        print(f"   - RTREE enabled: {self.config.enable_rtree}")
    
    def _init_database(self):
        """Inizializza schema database SQLite."""
        with sqlite3.connect(self.db_path) as conn:
            print(f"🔧 Initializing database: {self.db_path}")
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            # Tabella principale
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_records (
                    id TEXT PRIMARY KEY,
                    prompt TEXT NOT NULL,
                    response TEXT NOT NULL,
                    prio_vec TEXT NOT NULL,  -- JSON
                    symbols TEXT NOT NULL,   -- JSON array
                    tags TEXT NOT NULL,      -- JSON array
                    created_at REAL NOT NULL,
                    last_used REAL NOT NULL,
                    hits INTEGER DEFAULT 0,
                    score REAL DEFAULT 1.0,
                    initial_score REAL DEFAULT 1.0,
                    metadata TEXT DEFAULT '{}'
                )
            """)
            
            # Indici per performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON memory_records(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_last_used ON memory_records(last_used)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_score ON memory_records(score)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_hits ON memory_records(hits)")
            
            # FTS5 per ricerca full-text
            if self.config.enable_fts:
                conn.execute("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS memory_fts USING fts5(
                        id UNINDEXED,
                        prompt,
                        response,
                        tags,
                        content='memory_records',
                        content_rowid='rowid'
                    )
                """)
                
                # Trigger per mantenere FTS sincronizzato
                conn.execute("""
                    CREATE TRIGGER IF NOT EXISTS memory_fts_insert AFTER INSERT ON memory_records
                    BEGIN
                        INSERT INTO memory_fts(id, prompt, response, tags)
                        VALUES (new.id, new.prompt, new.response, new.tags);
                    END
                """)
                
                conn.execute("""
                    CREATE TRIGGER IF NOT EXISTS memory_fts_delete AFTER DELETE ON memory_records
                    BEGIN
                        DELETE FROM memory_fts WHERE id = old.id;
                    END
                """)
            
            # RTREE per vettori di priorità (se abilitato)
            if self.config.enable_rtree:
                conn.execute("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS memory_rtree USING rtree(
                        id,
                        urgency_min, urgency_max,
                        risk_min, risk_max,
                        confidence_min, confidence_max,
                        priority_min, priority_max
                    )
                """)
            
            conn.commit()

    def _init_database_direct(self, conn):
        """Inizializza schema database su connessione esistente."""
        print(f"🔧 Initializing database direct: {self.db_path}")

        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=10000")
        conn.execute("PRAGMA temp_store=MEMORY")

        # Tabella principale
        conn.execute("""
            CREATE TABLE IF NOT EXISTS memory_records (
                id TEXT PRIMARY KEY,
                prompt TEXT NOT NULL,
                response TEXT NOT NULL,
                prio_vec TEXT NOT NULL,  -- JSON
                symbols TEXT NOT NULL,   -- JSON array
                tags TEXT NOT NULL,      -- JSON array
                created_at REAL NOT NULL,
                last_used REAL NOT NULL,
                hits INTEGER DEFAULT 0,
                score REAL DEFAULT 1.0,
                initial_score REAL DEFAULT 1.0,
                metadata TEXT DEFAULT '{}'
            )
        """)

        # Indici per performance
        conn.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON memory_records(created_at)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_last_used ON memory_records(last_used)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_score ON memory_records(score)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_hits ON memory_records(hits)")

        # FTS5 per ricerca full-text
        if self.config.enable_fts:
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS memory_fts USING fts5(
                    id UNINDEXED,
                    prompt,
                    response,
                    tags,
                    content='memory_records',
                    content_rowid='rowid'
                )
            """)

            # Trigger per mantenere FTS sincronizzato
            conn.execute("""
                CREATE TRIGGER IF NOT EXISTS memory_fts_insert AFTER INSERT ON memory_records
                BEGIN
                    INSERT INTO memory_fts(id, prompt, response, tags)
                    VALUES (new.id, new.prompt, new.response, new.tags);
                END
            """)

            conn.execute("""
                CREATE TRIGGER IF NOT EXISTS memory_fts_delete AFTER DELETE ON memory_records
                BEGIN
                    DELETE FROM memory_fts WHERE id = old.id;
                END
            """)

        # RTREE per vettori di priorità (se abilitato)
        if self.config.enable_rtree:
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS memory_rtree USING rtree(
                    id,
                    urgency_min, urgency_max,
                    risk_min, risk_max,
                    confidence_min, confidence_max,
                    priority_min, priority_max
                )
            """)

        conn.commit()

    def _get_connection(self):
        """Ottiene connessione database."""
        if self._conn:
            return self._conn
        else:
            return sqlite3.connect(self.db_path)

    def append(self,
               prompt: str, 
               response: str, 
               prio_vec: Union[PriorityVector, Dict[str, float]], 
               symbols: Optional[List[str]] = None,
               tags: Optional[List[str]] = None,
               metadata: Optional[Dict[str, Any]] = None) -> MemoryRecord:
        """
        Aggiunge un record alla memoria.
        
        Args:
            prompt: Prompt originale
            response: Risposta generata
            prio_vec: Vettore di priorità
            symbols: Simboli NEUROGLYPH
            tags: Tags semantici
            metadata: Metadati aggiuntivi
            
        Returns:
            MemoryRecord creato
            
        Target: ≤0.5ms per record
        """
        start_time = time.time()
        
        # Converte PriorityVector in dict se necessario
        if isinstance(prio_vec, PriorityVector):
            prio_dict = {
                'urgency': prio_vec.urgency,
                'risk': prio_vec.risk,
                'domain': prio_vec.domain.value,
                'confidence': prio_vec.confidence
            }
        else:
            prio_dict = prio_vec
        
        # Crea record
        record = MemoryRecord(
            prompt=prompt,
            response=response,
            prio_vec=prio_dict,
            symbols=symbols or [],
            tags=set(tags or []),
            metadata=metadata or {}
        )
        
        with self.lock:
            # Verifica limite record
            if self.count() >= self.config.max_records:
                self._evict_records()

            # Inserisce in database
            if self._conn:
                conn = self._conn
                conn.execute("""
                    INSERT INTO memory_records
                    (id, prompt, response, prio_vec, symbols, tags,
                     created_at, last_used, hits, score, initial_score, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    str(record.id),
                    record.prompt,
                    record.response,
                    json.dumps(record.prio_vec),
                    json.dumps(record.symbols),
                    json.dumps(list(record.tags)),
                    record.created_at.timestamp(),
                    record.last_used.timestamp(),
                    record.hits,
                    record.score,
                    record.initial_score,
                    json.dumps(record.metadata)
                ))

                # Inserisce in RTREE se abilitato
                if self.config.enable_rtree:
                    pv = record.prio_vec
                    priority_score = record.priority_score
                    conn.execute("""
                        INSERT INTO memory_rtree
                        (id, urgency_min, urgency_max, risk_min, risk_max,
                         confidence_min, confidence_max, priority_min, priority_max)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        hash(str(record.id)) % (2**63),  # Convert UUID to int
                        pv['urgency'], pv['urgency'],
                        pv['risk'], pv['risk'],
                        pv['confidence'], pv['confidence'],
                        priority_score, priority_score
                    ))

                conn.commit()
            else:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT INTO memory_records
                        (id, prompt, response, prio_vec, symbols, tags,
                         created_at, last_used, hits, score, initial_score, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        str(record.id),
                        record.prompt,
                        record.response,
                        json.dumps(record.prio_vec),
                        json.dumps(record.symbols),
                        json.dumps(list(record.tags)),
                        record.created_at.timestamp(),
                        record.last_used.timestamp(),
                        record.hits,
                        record.score,
                        record.initial_score,
                        json.dumps(record.metadata)
                    ))

                    # Inserisce in RTREE se abilitato
                    if self.config.enable_rtree:
                        pv = record.prio_vec
                        priority_score = record.priority_score
                        conn.execute("""
                            INSERT INTO memory_rtree
                            (id, urgency_min, urgency_max, risk_min, risk_max,
                             confidence_min, confidence_max, priority_min, priority_max)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            hash(str(record.id)) % (2**63),  # Convert UUID to int
                            pv['urgency'], pv['urgency'],
                            pv['risk'], pv['risk'],
                            pv['confidence'], pv['confidence'],
                            priority_score, priority_score
                        ))

                    conn.commit()
        
        processing_time = time.time() - start_time
        
        if processing_time > 0.0005:  # 0.5ms target
            print(f"⚠️ Append slow: {processing_time*1000:.2f}ms (target: <0.5ms)")
        
        return record
    
    def search(self, query: MemorySearchQuery) -> List[MemorySearchResult]:
        """
        Cerca record in memoria.
        
        Args:
            query: Query di ricerca
            
        Returns:
            Lista di risultati ordinati per rilevanza
            
        Target: ≤5ms per 1k records
        """
        start_time = time.time()
        
        with self.lock:
            candidates = self._get_candidates(query)
            results = self._score_candidates(candidates, query)
            
            # Ordina per rilevanza e applica limit
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            results = results[query.offset:query.offset + query.limit]
            
            # Aggiorna statistiche utilizzo
            for result in results:
                self._update_record_usage(result.record.id)
        
        processing_time = time.time() - start_time
        
        if processing_time > 0.005:  # 5ms target
            print(f"⚠️ Search slow: {processing_time*1000:.2f}ms (target: <5ms)")
        
        return results
    
    def _get_candidates(self, query: MemorySearchQuery) -> List[MemoryRecord]:
        """Ottiene candidati per la ricerca."""
        candidates = []
        
        with sqlite3.connect(self.db_path) as conn:
            # Base query
            sql = "SELECT * FROM memory_records WHERE 1=1"
            params = []
            
            # Filtro per score minimo
            if query.min_score > 0:
                sql += " AND score >= ?"
                params.append(query.min_score)
            
            # Filtro per età massima
            if query.max_age_seconds:
                min_timestamp = (datetime.now(timezone.utc) - 
                               timedelta(seconds=query.max_age_seconds)).timestamp()
                sql += " AND created_at >= ?"
                params.append(min_timestamp)
            
            # Ricerca full-text se disponibile
            if query.text and self.config.enable_fts:
                fts_sql = """
                    SELECT mr.* FROM memory_records mr
                    JOIN memory_fts mf ON mr.id = mf.id
                    WHERE memory_fts MATCH ?
                """
                fts_params = [query.text]
                
                cursor = conn.execute(fts_sql, fts_params)
                fts_candidates = [self._row_to_record(row) for row in cursor.fetchall()]
                candidates.extend(fts_candidates)
            
            # Query normale se non FTS o come fallback
            if not query.text or not self.config.enable_fts or not candidates:
                if query.text:
                    sql += " AND (prompt LIKE ? OR response LIKE ?)"
                    params.extend([f"%{query.text}%", f"%{query.text}%"])
                
                # Limita candidati per performance
                sql += " ORDER BY score DESC LIMIT 1000"
                
                cursor = conn.execute(sql, params)
                normal_candidates = [self._row_to_record(row) for row in cursor.fetchall()]
                candidates.extend(normal_candidates)
        
        # Rimuove duplicati
        seen_ids = set()
        unique_candidates = []
        for candidate in candidates:
            if candidate.id not in seen_ids:
                seen_ids.add(candidate.id)
                unique_candidates.append(candidate)
        
        return unique_candidates
    
    def _score_candidates(self, candidates: List[MemoryRecord], 
                         query: MemorySearchQuery) -> List[MemorySearchResult]:
        """Calcola score di rilevanza per i candidati."""
        results = []
        
        for candidate in candidates:
            score = 0.0
            reasons = []
            
            # Score basato su priority vector
            if query.target_prio_vec:
                prio_score = self._calculate_prio_similarity(
                    candidate.prio_vec, query.target_prio_vec
                )
                score += prio_score * query.weight_prio
                reasons.append(f"priority_similarity: {prio_score:.3f}")
            
            # Score basato su testo
            if query.text:
                text_score = self._calculate_text_similarity(candidate, query.text)
                score += text_score * query.weight_text
                reasons.append(f"text_similarity: {text_score:.3f}")
            
            # Bonus per riutilizzo
            reuse_score = min(math.log(1 + candidate.hits) * 0.1, 1.0)
            score += reuse_score * query.weight_reuse
            reasons.append(f"reuse_bonus: {reuse_score:.3f}")
            
            # Applica filtri
            if self._passes_filters(candidate, query):
                results.append(MemorySearchResult(
                    record=candidate,
                    relevance_score=score,
                    match_reasons=reasons
                ))
        
        return results
    
    def _calculate_prio_similarity(self, prio1: Dict[str, float], 
                                  prio2: Dict[str, float]) -> float:
        """Calcola similarità tra vettori di priorità."""
        # Estrae valori numerici
        vec1 = [prio1.get('urgency', 0), prio1.get('risk', 0), prio1.get('confidence', 0)]
        vec2 = [prio2.get('urgency', 0), prio2.get('risk', 0), prio2.get('confidence', 0)]
        
        # Calcola cosine similarity
        try:
            similarity = 1.0 - cosine(vec1, vec2)
            return max(0.0, similarity)
        except:
            return 0.0
    
    def _calculate_text_similarity(self, record: MemoryRecord, query_text: str) -> float:
        """Calcola similarità testuale semplice."""
        query_lower = query_text.lower()
        prompt_lower = record.prompt.lower()
        response_lower = record.response.lower()
        
        # Conta parole in comune
        query_words = set(query_lower.split())
        prompt_words = set(prompt_lower.split())
        response_words = set(response_lower.split())
        
        # Jaccard similarity
        prompt_intersection = len(query_words & prompt_words)
        prompt_union = len(query_words | prompt_words)
        prompt_jaccard = prompt_intersection / prompt_union if prompt_union > 0 else 0
        
        response_intersection = len(query_words & response_words)
        response_union = len(query_words | response_words)
        response_jaccard = response_intersection / response_union if response_union > 0 else 0
        
        return max(prompt_jaccard, response_jaccard)
    
    def _passes_filters(self, record: MemoryRecord, query: MemorySearchQuery) -> bool:
        """Verifica se il record passa i filtri."""
        # Filtro domini
        if query.domains:
            if record.domain not in query.domains:
                return False
        
        # Filtro tags
        if query.tags:
            if not any(tag in record.tags for tag in query.tags):
                return False
        
        # Filtro simboli
        if query.symbols:
            if not any(symbol in record.symbols for symbol in query.symbols):
                return False
        
        return True
    
    def _update_record_usage(self, record_id: UUID):
        """Aggiorna statistiche utilizzo record."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE memory_records 
                SET hits = hits + 1, last_used = ?
                WHERE id = ?
            """, (time.time(), str(record_id)))
            conn.commit()
    
    def _row_to_record(self, row: Tuple) -> MemoryRecord:
        """Converte riga database in MemoryRecord."""
        return MemoryRecord(
            id=UUID(row[0]),
            prompt=row[1],
            response=row[2],
            prio_vec=json.loads(row[3]),
            symbols=json.loads(row[4]),
            tags=set(json.loads(row[5])),
            created_at=datetime.fromtimestamp(row[6], timezone.utc),
            last_used=datetime.fromtimestamp(row[7], timezone.utc),
            hits=row[8],
            score=row[9],
            initial_score=row[10],
            metadata=json.loads(row[11])
        )
    
    def count(self) -> int:
        """Conta record in memoria."""
        if self._conn:
            try:
                cursor = self._conn.execute("SELECT COUNT(*) FROM memory_records")
                return cursor.fetchone()[0]
            except sqlite3.OperationalError:
                return 0
        else:
            with sqlite3.connect(self.db_path) as conn:
                try:
                    cursor = conn.execute("SELECT COUNT(*) FROM memory_records")
                    return cursor.fetchone()[0]
                except sqlite3.OperationalError:
                    return 0
    
    def _evict_records(self):
        """Rimuove record secondo policy di eviction."""
        batch_size = self.config.eviction_batch_size
        
        with sqlite3.connect(self.db_path) as conn:
            if self.config.eviction_policy == "lru":
                # LRU puro
                conn.execute("""
                    DELETE FROM memory_records 
                    WHERE id IN (
                        SELECT id FROM memory_records 
                        ORDER BY last_used ASC 
                        LIMIT ?
                    )
                """, (batch_size,))
            
            elif self.config.eviction_policy == "lru_low_score":
                # LRU + low score
                conn.execute("""
                    DELETE FROM memory_records 
                    WHERE id IN (
                        SELECT id FROM memory_records 
                        ORDER BY score ASC, last_used ASC 
                        LIMIT ?
                    )
                """, (batch_size,))
            
            elif self.config.eviction_policy == "score_only":
                # Solo score basso
                conn.execute("""
                    DELETE FROM memory_records 
                    WHERE id IN (
                        SELECT id FROM memory_records 
                        ORDER BY score ASC 
                        LIMIT ?
                    )
                """, (batch_size,))
            
            conn.commit()
    
    def decay_update(self):
        """Aggiorna score con decay esponenziale."""
        if time.time() - self._last_decay_update < self.config.decay_update_interval:
            return
        
        with sqlite3.connect(self.db_path) as conn:
            # Aggiorna tutti i score
            cursor = conn.execute("SELECT id, created_at, hits, initial_score FROM memory_records")
            
            updates = []
            now = time.time()
            
            for row in cursor.fetchall():
                record_id, created_at, hits, initial_score = row
                age_seconds = now - created_at
                
                # Calcola nuovo score
                decay_factor = math.exp(-self.config.decay_lambda * age_seconds)
                reuse_bonus = math.log(1 + hits) * 0.1
                new_score = min((initial_score * decay_factor) + reuse_bonus, 1.0)
                
                updates.append((new_score, record_id))
            
            # Batch update
            conn.executemany("UPDATE memory_records SET score = ? WHERE id = ?", updates)
            conn.commit()
        
        self._last_decay_update = time.time()
        print(f"🧠 Memory decay update: {len(updates)} records updated")
    
    def get_stats(self) -> MemoryStats:
        """Ottiene statistiche memoria."""
        with sqlite3.connect(self.db_path) as conn:
            # Statistiche base
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total,
                    AVG(score) as avg_score,
                    MAX(hits) as max_hits,
                    MIN(created_at) as oldest
                FROM memory_records
            """)
            row = cursor.fetchone()
            
            total_records = row[0]
            avg_score = row[1] or 0.0
            max_hits = row[2] or 0
            oldest_timestamp = row[3] or time.time()
            oldest_age = time.time() - oldest_timestamp
            
            # Statistiche per dominio
            cursor = conn.execute("""
                SELECT json_extract(prio_vec, '$.domain') as domain, COUNT(*)
                FROM memory_records
                GROUP BY domain
            """)
            domain_counts = dict(cursor.fetchall())
            
            # Statistiche temporali
            now = time.time()
            hour_ago = now - 3600
            day_ago = now - 86400
            week_ago = now - 604800
            
            cursor = conn.execute("SELECT COUNT(*) FROM memory_records WHERE created_at >= ?", (hour_ago,))
            records_last_hour = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM memory_records WHERE created_at >= ?", (day_ago,))
            records_last_day = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM memory_records WHERE created_at >= ?", (week_ago,))
            records_last_week = cursor.fetchone()[0]
        
        return MemoryStats(
            total_records=total_records,
            avg_score=avg_score,
            oldest_record_age=oldest_age,
            most_used_hits=max_hits,
            domain_counts=domain_counts,
            records_last_hour=records_last_hour,
            records_last_day=records_last_day,
            records_last_week=records_last_week
        )

    def retrieve(self, priority_vector: PriorityVector,
                parsed_prompt: Any, limit: int = 5) -> MemoryContext:
        """
        Recupera contesto di memoria per il reasoning.

        Args:
            priority_vector: Vettore di priorità dal Context Prioritizer
            parsed_prompt: Prompt parsato (per compatibilità)
            limit: Numero massimo di record da recuperare

        Returns:
            MemoryContext con record rilevanti
        """
        # Crea query di ricerca
        query = MemorySearchQuery(
            text=getattr(parsed_prompt, 'original_text', None),
            target_prio_vec={
                'urgency': priority_vector.urgency,
                'risk': priority_vector.risk,
                'domain': priority_vector.domain.value,
                'confidence': priority_vector.confidence
            },
            limit=limit
        )

        # Esegui ricerca
        results = self.search(query)

        # Converte in MemoryContext
        symbols = []
        episodes = []
        errors = []
        patterns = []
        examples = []

        for result in results:
            record = result.record

            # Categorizza record per tipo
            if any('error' in tag or 'bug' in tag for tag in record.tags):
                errors.append({
                    'id': str(record.id),
                    'prompt': record.prompt,
                    'response': record.response,
                    'relevance': result.relevance_score,
                    'tags': list(record.tags),
                    'symbols': record.symbols
                })

            elif record.domain in ['code', 'system']:
                examples.append({
                    'id': str(record.id),
                    'prompt': record.prompt,
                    'response': record.response,
                    'relevance': result.relevance_score,
                    'domain': record.domain,
                    'symbols': record.symbols
                })

            else:
                episodes.append({
                    'id': str(record.id),
                    'prompt': record.prompt,
                    'response': record.response,
                    'relevance': result.relevance_score,
                    'priority_score': record.priority_score,
                    'hits': record.hits
                })

            # Estrai simboli unici
            for symbol in record.symbols:
                if symbol not in [s.get('symbol') for s in symbols]:
                    symbols.append({
                        'symbol': symbol,
                        'frequency': 1,
                        'contexts': [record.domain]
                    })

        return MemoryContext(
            symbols=symbols,
            episodes=episodes,
            errors=errors,
            patterns=patterns,
            examples=examples,
            metadata={
                'query_priority': {
                    'urgency': priority_vector.urgency,
                    'risk': priority_vector.risk,
                    'domain': priority_vector.domain.value
                },
                'total_results': len(results),
                'avg_relevance': sum(r.relevance_score for r in results) / len(results) if results else 0
            }
        )

    def audit_high_risk(self, min_risk: float = 0.6, limit: int = 100) -> List[MemoryRecord]:
        """
        Esporta record ad alto rischio per audit.

        Args:
            min_risk: Soglia minima di rischio
            limit: Numero massimo di record

        Returns:
            Lista di record ad alto rischio
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM memory_records
                WHERE json_extract(prio_vec, '$.risk') >= ?
                ORDER BY json_extract(prio_vec, '$.risk') DESC, created_at DESC
                LIMIT ?
            """, (min_risk, limit))

            return [self._row_to_record(row) for row in cursor.fetchall()]

    def snapshot(self, output_path: Optional[str] = None) -> str:
        """
        Crea snapshot compresso della memoria.

        Args:
            output_path: Percorso file output (default: auto-generato)

        Returns:
            Percorso file snapshot creato
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"neuroglyph_memory_snapshot_{timestamp}.db"

        # Copia database
        import shutil
        if self.db_path != ":memory:":
            shutil.copy2(self.db_path, output_path)
        else:
            # Per database in memoria, esporta in nuovo file
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(output_path) as dest:
                    source.backup(dest)

        # Comprimi se richiesto
        if self.config.snapshot_compression != "none":
            compressed_path = f"{output_path}.{self.config.snapshot_compression}"

            if self.config.snapshot_compression == "zstd":
                try:
                    import zstandard as zstd
                    with open(output_path, 'rb') as src:
                        with open(compressed_path, 'wb') as dst:
                            compressor = zstd.ZstdCompressor(level=3)
                            compressor.copy_stream(src, dst)

                    # Rimuovi file non compresso
                    Path(output_path).unlink()
                    output_path = compressed_path
                except ImportError:
                    print("⚠️ zstandard not available, using gzip")
                    self.config.snapshot_compression = "gzip"

            if self.config.snapshot_compression == "gzip":
                import gzip
                with open(output_path, 'rb') as src:
                    with gzip.open(compressed_path, 'wb') as dst:
                        dst.write(src.read())

                Path(output_path).unlink()
                output_path = compressed_path

        print(f"🧠 Memory snapshot created: {output_path}")
        return output_path

    def load_snapshot(self, snapshot_path: str):
        """
        Carica snapshot nella memoria.

        Args:
            snapshot_path: Percorso file snapshot
        """
        # Decomprimi se necessario
        temp_path = snapshot_path

        if snapshot_path.endswith('.zstd'):
            try:
                import zstandard as zstd
                temp_path = snapshot_path[:-5]  # Rimuovi .zstd
                with open(snapshot_path, 'rb') as src:
                    with open(temp_path, 'wb') as dst:
                        decompressor = zstd.ZstdDecompressor()
                        decompressor.copy_stream(src, dst)
            except ImportError:
                raise ValueError("zstandard required for .zstd files")

        elif snapshot_path.endswith('.gz'):
            import gzip
            temp_path = snapshot_path[:-3]  # Rimuovi .gz
            with gzip.open(snapshot_path, 'rb') as src:
                with open(temp_path, 'wb') as dst:
                    dst.write(src.read())

        # Carica database
        if self.db_path != ":memory:":
            import shutil
            shutil.copy2(temp_path, self.db_path)
        else:
            # Per database in memoria, importa da file
            with sqlite3.connect(temp_path) as source:
                with sqlite3.connect(self.db_path) as dest:
                    source.backup(dest)

        # Pulisci file temporaneo se creato
        if temp_path != snapshot_path:
            Path(temp_path).unlink()

        print(f"🧠 Memory snapshot loaded: {snapshot_path}")

    def vacuum(self):
        """Ottimizza database (VACUUM)."""
        if time.time() - self._last_vacuum < self.config.vacuum_interval:
            return

        with sqlite3.connect(self.db_path) as conn:
            conn.execute("VACUUM")
            conn.commit()

        self._last_vacuum = time.time()
        print("🧠 Memory database vacuumed")

    def close(self):
        """Chiude connessioni e pulisce risorse."""
        # Esegui decay update finale
        self.decay_update()

        # Vacuum se necessario
        self.vacuum()

        print("🧠 NGMemory closed")
