#!/usr/bin/env python3
"""
NEUROGLYPH Semantic Compressor v2.0

Implementa compressione semantica avanzata con pattern recognition
e trasformazione intelligente di strutture di codice ricorrenti.
"""

import ast
import re
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class CompressionLevel(Enum):
    """Livelli di compressione semantica."""
    BASIC = 1       # Funzioni, if base
    INTERMEDIATE = 2 # Loops, classi
    ADVANCED = 3    # Async, try/catch, pattern matching
    EXPERT = 4      # Decoratori, context managers
    ULTRA = 5       # Algoritmi completi, design patterns

@dataclass
class CompressionPattern:
    """Definisce un pattern di compressione."""
    name: str
    regex_pattern: str
    symbol_replacement: str
    ast_pattern: Optional[str] = None
    min_level: CompressionLevel = CompressionLevel.BASIC
    language: str = "python"
    description: str = ""

@dataclass
class CompressionResult:
    """Risultato della compressione semantica."""
    compressed_symbols: List[str]
    compression_metadata: Dict[str, Any]
    original_length: int
    compressed_length: int
    compression_ratio: float
    patterns_applied: List[str]

class SemanticCompressor:
    """
    Compressore semantico NEUROGLYPH per trasformazione intelligente
    di codice in rappresentazione simbolica compressa.
    """
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il compressore semantico.
        
        Args:
            registry_path: Percorso al registry simbolico NEUROGLYPH
        """
        self.registry_path = registry_path
        self.compression_patterns = self._initialize_patterns()
        self.symbol_mappings = self._load_symbol_mappings()
        
        print("🔧 SemanticCompressor inizializzato")
        print(f"   - Pattern caricati: {len(self.compression_patterns)}")
        print(f"   - Simboli mappati: {len(self.symbol_mappings)}")
    
    def _initialize_patterns(self) -> List[CompressionPattern]:
        """Inizializza i pattern di compressione."""
        patterns = []
        
        # LIVELLO 1 - BASIC
        patterns.extend([
            CompressionPattern(
                name="function_definition",
                regex_pattern=r"def\s+(\w+)\s*\([^)]*\)\s*:",
                symbol_replacement="⟨⟩",
                min_level=CompressionLevel.BASIC,
                description="Definizione funzione base"
            ),
            CompressionPattern(
                name="if_statement",
                regex_pattern=r"if\s+[^:]+:",
                symbol_replacement="◊",
                min_level=CompressionLevel.BASIC,
                description="Statement if base"
            ),
            CompressionPattern(
                name="else_clause",
                regex_pattern=r"else\s*:",
                symbol_replacement="☇",
                min_level=CompressionLevel.BASIC,
                description="Clausola else"
            ),
            CompressionPattern(
                name="aug_assign",
                regex_pattern=r"(\w+)\s*([\+\-\*/]=)\s*([^\n]+)",
                symbol_replacement="⊞",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Augmented assignment"
            ),
            CompressionPattern(
                name="variable_assignment",
                regex_pattern=r"(\w+)\s*=\s*(.+)",
                symbol_replacement="⟦⟧",
                min_level=CompressionLevel.BASIC,
                description="Assegnazione variabile"
            ),
            # FASE 1 - PATTERN CRITICI MANCANTI
            CompressionPattern(
                name="return_statement",
                regex_pattern=r"return\s+[^;\n]+",
                symbol_replacement="⤴",
                min_level=CompressionLevel.BASIC,
                description="Statement return"
            ),
            CompressionPattern(
                name="return_simple",
                regex_pattern=r"return\s*$",
                symbol_replacement="⤴",
                min_level=CompressionLevel.BASIC,
                description="Return semplice"
            ),
            # S-1: PATTERN FOR/WHILE/LISTCOMP
            CompressionPattern(
                name="for_loop",
                regex_pattern=r"for\s+\w+\s+in\s+[^:]+:",
                symbol_replacement="⟲",
                min_level=CompressionLevel.BASIC,
                description="For loop"
            ),
            CompressionPattern(
                name="while_loop",
                regex_pattern=r"while\s+[^:]+:",
                symbol_replacement="⟳",
                min_level=CompressionLevel.BASIC,
                description="While loop"
            ),
            CompressionPattern(
                name="list_comprehension",
                regex_pattern=r"\[[^\]]+\s+for\s+\w+\s+in\s+[^\]]+\]",
                symbol_replacement="⟔",
                min_level=CompressionLevel.INTERMEDIATE,
                description="List comprehension"
            ),
        ])
        
        # LIVELLO 2 - INTERMEDIATE
        patterns.extend([
            CompressionPattern(
                name="for_loop",
                regex_pattern=r"for\s+\w+\s+in\s+[^:]+:",
                symbol_replacement="⟲",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Loop for"
            ),
            CompressionPattern(
                name="while_loop",
                regex_pattern=r"while\s+[^:]+:",
                symbol_replacement="⟳",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Loop while"
            ),
            CompressionPattern(
                name="class_definition",
                regex_pattern=r"class\s+(\w+)(?:\([^)]*\))?\s*:",
                symbol_replacement="⟡",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Definizione classe"
            ),
            CompressionPattern(
                name="import_statement",
                regex_pattern=r"(?:from\s+\w+\s+)?import\s+[\w\s,]+",
                symbol_replacement="⟢",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Statement import"
            ),
            # S-1: OPERATORI BINARI BASILARI
            CompressionPattern(
                name="binary_add",
                regex_pattern=r"\w+\s*\+\s*\w+",
                symbol_replacement="⊕",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Addizione binaria"
            ),
            CompressionPattern(
                name="binary_sub",
                regex_pattern=r"\w+\s*-\s*\w+",
                symbol_replacement="⊖",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Sottrazione binaria"
            ),
            CompressionPattern(
                name="binary_mul",
                regex_pattern=r"\w+\s*\*\s*\w+",
                symbol_replacement="⊗",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Moltiplicazione binaria"
            ),
            CompressionPattern(
                name="binary_div",
                regex_pattern=r"\w+\s*/\s*\w+",
                symbol_replacement="⊘",
                min_level=CompressionLevel.INTERMEDIATE,
                description="Divisione binaria"
            ),
        ])
        
        # LIVELLO 3 - ADVANCED
        patterns.extend([
            CompressionPattern(
                name="async_function",
                regex_pattern=r"async\s+def\s+(\w+)\s*\([^)]*\)\s*:",
                symbol_replacement="⊶",
                min_level=CompressionLevel.ADVANCED,
                description="Funzione asincrona"
            ),
            CompressionPattern(
                name="await_expression",
                regex_pattern=r"await\s+[\w\.()]+",
                symbol_replacement="⊷",
                min_level=CompressionLevel.ADVANCED,
                description="Espressione await"
            ),
            CompressionPattern(
                name="try_except",
                regex_pattern=r"try\s*:.*?except.*?:",
                symbol_replacement="⟐",
                min_level=CompressionLevel.ADVANCED,
                description="Blocco try/except"
            ),
            CompressionPattern(
                name="list_comprehension",
                regex_pattern=r"\[[^\]]+\s+for\s+\w+\s+in\s+[^\]]+\]",
                symbol_replacement="⟔",
                min_level=CompressionLevel.ADVANCED,
                description="List comprehension"
            ),
        ])
        
        # LIVELLO 4 - EXPERT
        patterns.extend([
            CompressionPattern(
                name="decorator",
                regex_pattern=r"@\w+(?:\([^)]*\))?",
                symbol_replacement="⟢",
                min_level=CompressionLevel.EXPERT,
                description="Decoratore"
            ),
            CompressionPattern(
                name="context_manager",
                regex_pattern=r"with\s+[^:]+:",
                symbol_replacement="⟕",
                min_level=CompressionLevel.EXPERT,
                description="Context manager"
            ),
            CompressionPattern(
                name="generator_expression",
                regex_pattern=r"\([^\)]+\s+for\s+\w+\s+in\s+[^\)]+\)",
                symbol_replacement="⟖",
                min_level=CompressionLevel.EXPERT,
                description="Generator expression"
            ),
        ])
        
        # LIVELLO 5 - ULTRA (Pattern complessi)
        patterns.extend([
            CompressionPattern(
                name="for_if_return_pattern",
                regex_pattern=r"for\s+\w+\s+in\s+[^:]+:\s*if\s+[^:]+:\s*return\s+\w+",
                symbol_replacement="⟳◊⟵",  # Sequenza compressa
                min_level=CompressionLevel.ULTRA,
                description="Pattern for-if-return"
            ),
            CompressionPattern(
                name="async_await_loop",
                regex_pattern=r"async\s+def.*?for.*?await",
                symbol_replacement="⊶⟲",  # Async loop pattern
                min_level=CompressionLevel.ULTRA,
                description="Pattern async-await-loop"
            ),
            CompressionPattern(
                name="if_elif_else_chain",
                regex_pattern=r"if\s+[^:]+:.*?elif\s+[^:]+:.*?else\s*:",
                symbol_replacement="⟈",  # If-else nest
                min_level=CompressionLevel.ULTRA,
                description="Catena if-elif-else"
            ),
            CompressionPattern(
                name="recursive_function_call",
                regex_pattern=r"return\s+\w+\([^)]*\)\s*\+.*?\+\s*\w+\([^)]*\)",
                symbol_replacement="⟲⟵",  # Ricorsione
                min_level=CompressionLevel.ULTRA,
                description="Chiamata ricorsiva con concatenazione"
            ),
            CompressionPattern(
                name="conditional_assignment",
                regex_pattern=r"\w+\s*=\s*\[.*?\s+for\s+\w+\s+in\s+.*?\s+if\s+.*?\]",
                symbol_replacement="⟔◊",  # List comprehension condizionale
                min_level=CompressionLevel.ULTRA,
                description="Assegnazione con list comprehension condizionale"
            ),
        ])
        
        return patterns
    
    def _load_symbol_mappings(self) -> Dict[str, str]:
        """Carica mappature simbolo → significato dal registry."""
        # Per ora usiamo mappature hardcoded, poi integreremo con registry
        return {
            "⟨⟩": "function_definition",
            "◊": "if_statement",
            "⟲": "for_loop",
            "⟳": "while_loop",
            "⟡": "class_definition",
            "⟢": "import_statement",
            "⊶": "async_function",
            "⊷": "await_expression",
            "⟐": "try_except",
            "⟔": "list_comprehension",
            "⟕": "context_manager",
            "⟖": "generator_expression",
            "⟈": "if_elif_else_chain",
            "⟳◊⟵": "for_if_return_pattern",
            "⊶⟲": "async_await_loop",
            "⟦⟧": "variable_assignment",
            # FASE 1 - SIMBOLI MANCANTI CRITICI
            "⤴": "return_statement",
            "⟇": "try_block",
            "⟈": "except_handler",
            "⟉": "finally_block",
            "⊕": "add_operator",
            "⊖": "sub_operator",
            "⊗": "mul_operator",
            "⊘": "div_operator",
            "≡": "eq_operator",
            "≠": "noteq_operator",
            "≤": "lte_operator",
            "≥": "gte_operator",
            "∧": "and_operator",
            "∨": "or_operator",
            "¬": "not_operator"
        }
    
    def _ultra_semantic_compression(self, 
                                   source_code: str,
                                   encoding_level: int = 3,
                                   language: str = "python") -> CompressionResult:
        """
        Implementa compressione semantica ultra-avanzata.
        
        Args:
            source_code: Codice sorgente da comprimere
            encoding_level: Livello di compressione (1-5)
            language: Linguaggio di programmazione
            
        Returns:
            CompressionResult con simboli compressi e metadati
        """
        print(f"🔧 Compressione semantica livello {encoding_level}")
        
        # Normalizza codice
        normalized_code = self._normalize_code(source_code)
        original_length = len(normalized_code)
        
        # Applica pattern di compressione
        compressed_symbols = []
        patterns_applied = []
        compression_metadata = {
            "encoding_level": encoding_level,
            "language": language,
            "original_lines": len(source_code.split('\n')),
            "pattern_mappings": {}
        }

        # 1️⃣ INIZIALIZZA SYMBOL REGISTRY per decodifica ricorsiva
        self.symbol_registry = {}
        
        # Filtra pattern per livello
        applicable_patterns = [
            p for p in self.compression_patterns 
            if p.min_level.value <= encoding_level and p.language == language
        ]
        
        print(f"   - Pattern applicabili: {len(applicable_patterns)}")
        
        # FASE 2 - ENCODER CON PAYLOAD E METADATI + TOKEN END
        working_code = normalized_code
        payload_index = 0
        compression_metadata["payload"] = []  # Lista ordinata di valori
        compression_metadata["symbol_metadata"] = {}  # Metadati per simbolo

        # 🛠 STEP 2: TRACCIA BLOCCHI APERTI PER TOKEN END
        block_stack = []

        for pattern in sorted(applicable_patterns, key=lambda p: p.min_level.value, reverse=True):
            matches = list(re.finditer(pattern.regex_pattern, working_code, re.MULTILINE | re.DOTALL))

            for match in matches:
                matched_text = match.group(0)
                stripped = matched_text.lstrip()

                # 2️⃣ PATCH: IGNORA DECORATOR E SUBSCRIPT-ASSIGN (ma non list-comp)
                if stripped.startswith("@"):
                    print(f"   -- decorator ignorato: {matched_text[:30]}...")
                    continue

                # FASE 2 - ESTRAI METADATI SPECIFICI
                symbol_metadata = self._extract_symbol_metadata(pattern, match, payload_index)

                # 1️⃣ DUAL-FORMAT: BASIC vs ADVANCED
                if symbol_metadata.get("payload_needed"):
                    if encoding_level == 1:  # BASIC level - simboli nudi
                        symbol_token = pattern.symbol_replacement
                        # Salva payload per compatibilità ma non usare indice
                        compression_metadata["payload"].append(symbol_metadata["payload_value"])
                        payload_index += 1
                    else:  # INTERMEDIATE+ - simboli con payload
                        symbol_token = f"{pattern.symbol_replacement}§{payload_index}§"
                        compression_metadata["payload"].append(symbol_metadata["payload_value"])
                        payload_index += 1
                else:
                    symbol_token = pattern.symbol_replacement

                working_code = working_code.replace(matched_text, symbol_token, 1)
                compressed_symbols.append(symbol_token)
                patterns_applied.append(pattern.name)

                # 🛠 STEP 2: TRACCIA BLOCCHI (TOKEN END DISABILITATO PER ORA)
                if pattern.name in ["function_definition", "for_loop", "while_loop", "if_statement", "class_definition"]:
                    block_stack.append(pattern.name)
                    # TODO: Implementare emissione token END basata su AST
                    # compressed_symbols.append("⟩")
                    # print(f"   Emesso token END per {pattern.name}")

                # Salva mapping per decompressione
                compression_metadata["pattern_mappings"][symbol_token] = {
                    "original_text": matched_text,
                    "pattern_name": pattern.name,
                    "description": pattern.description
                }

                # Salva metadati simbolo
                compression_metadata["symbol_metadata"][symbol_token] = symbol_metadata

                # 1️⃣ POPOLA SYMBOL REGISTRY per decodifica ricorsiva
                self.symbol_registry[symbol_token] = {
                    "original_text": matched_text,
                    "pattern_name": pattern.name
                }
        
        # Gestisci triplette ripetute (⟨⟩⟨⟩⟨⟩ → ⟨⟩³)
        if encoding_level >= 3:
            working_code = self._compress_repetitions(working_code, compression_metadata)

        # FASE 2 - TOKENIZZA SOLO SE RIMANE CODICE NON SIMBOLICO
        # Se il working_code contiene solo simboli e whitespace, non tokenizzare
        remaining_code = working_code.strip()
        if remaining_code and not self._is_only_symbols(remaining_code):
            remaining_tokens = self._tokenize_remaining(remaining_code)
            compressed_symbols.extend(remaining_tokens)
        
        compressed_length = len(' '.join(compressed_symbols))
        compression_ratio = (original_length - compressed_length) / original_length * 100
        
        # 1️⃣ POST-PROCESS PAYLOAD: Decodifica ricorsiva simboli nei payload
        # Implementato nel SymbolMapper durante la decodifica

        result = CompressionResult(
            compressed_symbols=compressed_symbols,
            compression_metadata=compression_metadata,
            original_length=original_length,
            compressed_length=compressed_length,
            compression_ratio=compression_ratio,
            patterns_applied=patterns_applied
        )
        
        print(f"✅ Compressione completata:")
        print(f"   - Simboli generati: {len(compressed_symbols)}")
        print(f"   - Pattern applicati: {len(set(patterns_applied))}")
        print(f"   - Ratio compressione: {compression_ratio:.1f}%")
        
        return result
    
    def _normalize_code(self, source_code: str) -> str:
        """Normalizza il codice per la compressione."""
        # Rimuovi commenti e docstring per focus sulla logica
        lines = []
        for line in source_code.split('\n'):
            stripped = line.strip()
            if not stripped.startswith('#') and not stripped.startswith('"""'):
                lines.append(line)
        
        return '\n'.join(lines)
    
    def _compress_repetitions(self, code: str, metadata: Dict) -> str:
        """Comprimi sequenze ripetute di simboli."""
        # Pattern per triplette ripetute
        repetition_patterns = [
            (r'(⟨⟩)\1\1', r'\1³'),  # ⟨⟩⟨⟩⟨⟩ → ⟨⟩³
            (r'(◊)\1\1', r'\1³'),   # ◊◊◊ → ◊³
            (r'(⟲)\1\1', r'\1³'),   # ⟲⟲⟲ → ⟲³
        ]
        
        for pattern, replacement in repetition_patterns:
            if re.search(pattern, code):
                code = re.sub(pattern, replacement, code)
                metadata["repetition_compression"] = True
        
        return code
    
    def _tokenize_remaining(self, code: str) -> List[str]:
        """Tokenizza il codice rimanente non compresso preservando strutture."""
        tokens = []

        # Pattern per preservare stringhe, numeri e identificatori
        patterns = [
            r'[⟨⟩◊⟲⟳⟡⟢⊶⊷⟐⟔⟕⟖⟈⟦⟧]+',  # Simboli NEUROGLYPH
            r'"[^"]*"',                           # Stringhe con virgolette doppie
            r"'[^']*'",                           # Stringhe con virgolette singole
            r'\b\d+\.?\d*\b',                     # Numeri
            r'\b[a-zA-Z_][a-zA-Z0-9_]*\b',        # Identificatori
            r'[+\-*/=<>!&|]+',                    # Operatori
            r'[()[\]{}]',                         # Parentesi e bracket
            r'[,;:]',                             # Punteggiatura
        ]

        # Combina pattern
        combined_pattern = '|'.join(f'({pattern})' for pattern in patterns)

        # Trova tutti i match
        matches = re.findall(combined_pattern, code)

        for match_groups in matches:
            for group in match_groups:
                if group and group.strip():
                    tokens.append(group.strip())

        return tokens

    def _post_process_payload(self, payload: str, symbol_registry: Dict[str, Any] = None) -> str:
        """Rimuove eventuali simboli grezzi ricorsivamente."""
        if not symbol_registry:
            return payload  # Non possiamo processare senza registry

        # Finché dentro ci sono simboli della forma «…§n§» sostituiscili
        pattern = r"[⟔⟲⟳⟡⊗⊕◊⟦⟧⤴⟨⟩][^\s]*§\d+§"
        max_iterations = 10  # Evita loop infiniti
        iteration = 0

        while re.search(pattern, payload) and iteration < max_iterations:
            parts = payload.split()
            new_parts = []
            for p in parts:
                if re.fullmatch(pattern, p):
                    # Cerca il simbolo nel registry
                    if p in symbol_registry:
                        new_parts.append(symbol_registry[p]["original_text"])
                    else:
                        new_parts.append(p)  # Mantieni se non trovato
                else:
                    new_parts.append(p)
            payload = " ".join(new_parts)
            iteration += 1

        return payload

    def _extract_symbol_metadata(self, pattern: CompressionPattern, match: re.Match, payload_index: int) -> Dict[str, Any]:
        """
        Estrae metadati specifici da un pattern match.

        Args:
            pattern: Pattern di compressione
            match: Match regex
            payload_index: Indice corrente del payload

        Returns:
            Dizionario con metadati del simbolo
        """
        metadata = {
            "pattern_name": pattern.name,
            "original_text": match.group(0),
            "payload_needed": False,
            "payload_value": None
        }

        # FASE 2 - ESTRAZIONE METADATI SPECIFICI PER PATTERN

        if pattern.name == "function_definition":
            # Estrai nome funzione
            func_match = re.search(r"def\s+(\w+)", match.group(0))
            if func_match:
                metadata["payload_needed"] = True
                metadata["payload_value"] = func_match.group(1)
                metadata["function_name"] = func_match.group(1)

        elif pattern.name == "return_statement":
            # Estrai valore di return
            return_match = re.search(r"return\s+(.+)", match.group(0))
            if return_match:
                return_value = return_match.group(1).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = return_value
                metadata["return_value"] = return_value

        elif pattern.name == "variable_assignment":
            # Estrai nome variabile e valore
            assign_match = re.search(r"(\w+)\s*=\s*(.+)", match.group(0))
            if assign_match:
                var_name = assign_match.group(1)
                var_value = assign_match.group(2).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = f"{var_name}={var_value}"
                metadata["variable_name"] = var_name
                metadata["variable_value"] = var_value

        elif pattern.name == "aug_assign":
            # 5️⃣ AUGASSIGN - ESTRAZIONE METADATI
            if len(match.groups()) >= 3:
                target = match.group(1)
                op = match.group(2)
                value = match.group(3).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = f"{target} {op} {value}"
                metadata["target"] = target
                metadata["operator"] = op
                metadata["right"] = value

        elif pattern.name == "if_statement":
            # Estrai condizione
            if_match = re.search(r"if\s+([^:]+)", match.group(0))
            if if_match:
                condition = if_match.group(1).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = condition
                metadata["condition"] = condition

        elif pattern.name == "class_definition":
            # Estrai nome classe
            class_match = re.search(r"class\s+(\w+)", match.group(0))
            if class_match:
                metadata["payload_needed"] = True
                metadata["payload_value"] = class_match.group(1)
                metadata["class_name"] = class_match.group(1)

        # S-1: METADATI PER NUOVI PATTERN
        elif pattern.name == "for_loop":
            # Estrai variabile e iterabile
            for_match = re.search(r"for\s+(\w+)\s+in\s+([^:]+)", match.group(0))
            if for_match:
                var_name = for_match.group(1)
                iterable = for_match.group(2).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = f"{var_name} in {iterable}"
                metadata["loop_var"] = var_name
                metadata["iterable"] = iterable

        elif pattern.name == "while_loop":
            # Estrai condizione
            while_match = re.search(r"while\s+([^:]+)", match.group(0))
            if while_match:
                condition = while_match.group(1).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = condition
                metadata["condition"] = condition

        elif pattern.name == "list_comprehension":
            # Estrai espressione, variabile e iterabile
            listcomp_match = re.search(r"\[([^\]]+)\s+for\s+(\w+)\s+in\s+([^\]]+)\]", match.group(0))
            if listcomp_match:
                expression = listcomp_match.group(1).strip()
                var_name = listcomp_match.group(2)
                iterable = listcomp_match.group(3).strip()
                metadata["payload_needed"] = True
                metadata["payload_value"] = f"{expression} for {var_name} in {iterable}"
                metadata["expression"] = expression
                metadata["loop_var"] = var_name
                metadata["iterable"] = iterable

        elif pattern.name in ["binary_add", "binary_sub", "binary_mul", "binary_div"]:
            # Estrai operandi
            op_symbol = {"binary_add": "+", "binary_sub": "-", "binary_mul": "*", "binary_div": "/"}[pattern.name]

            # S-1: REGEX CORRETTI PER OPERATORI (senza escape per * e +)
            if op_symbol == "*":
                op_pattern = r"(\w+)\s*\*\s*(\w+)"
            elif op_symbol == "+":
                op_pattern = r"(\w+)\s*\+\s*(\w+)"
            else:
                op_pattern = rf"(\w+)\s*\{re.escape(op_symbol)}\s*(\w+)"

            op_match = re.search(op_pattern, match.group(0))
            if op_match:
                left = op_match.group(1)
                right = op_match.group(2)
                # S-1: OPERATORI BINARI HANNO BISOGNO DI PAYLOAD
                metadata["payload_needed"] = True
                metadata["payload_value"] = f"{left} {op_symbol} {right}"
                metadata["left_operand"] = left
                metadata["right_operand"] = right
                metadata["operator"] = op_symbol
            else:
                # Se non riusciamo a estrarre operandi, non usiamo payload
                metadata["payload_needed"] = False

        return metadata

    def _is_only_symbols(self, code: str) -> bool:
        """
        Verifica se il codice contiene solo simboli NEUROGLYPH e whitespace.

        Args:
            code: Codice da verificare

        Returns:
            True se contiene solo simboli, False altrimenti
        """
        # Rimuovi whitespace
        clean_code = re.sub(r'\s+', '', code)

        if not clean_code:
            return True

        # Pattern per simboli NEUROGLYPH (inclusi quelli con payload)
        symbol_pattern = r'[⟨⟩◊⟲⟳⟡⟢⊶⊷⟐⟔⟕⟖⟈⟦⟧⤴⟇⟉⊕⊖⊗⊘≡≠≤≥∧∨¬§\d]+'

        # Sostituisci tutti i simboli con stringa vuota
        without_symbols = re.sub(symbol_pattern, '', clean_code)

        # Se rimane qualcosa, non è solo simboli
        return len(without_symbols) == 0

    def compress(self,
                source_code: str,
                encoding_level: int = 3,
                language: str = "python") -> CompressionResult:
        """
        Interfaccia pubblica per compressione semantica.
        
        Args:
            source_code: Codice da comprimere
            encoding_level: Livello di compressione (1-5)
            language: Linguaggio di programmazione
            
        Returns:
            CompressionResult con risultati della compressione
        """
        return self._ultra_semantic_compression(source_code, encoding_level, language)
