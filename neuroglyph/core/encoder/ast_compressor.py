#!/usr/bin/env python3
"""
AST-Based Compressor per NEUROGLYPH
Risolve i problemi di fidelity usando NodeTransformer invece di string compression.
"""

import ast
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class PlaceholderNode:
    """Nodo placeholder per AST compression."""
    symbol: str
    original_node: ast.AST
    metadata: Dict[str, Any]

@dataclass
class PlaceholderWhile(PlaceholderNode):
    """Placeholder per nodi While con body AST preservato."""
    condition_src: str
    body_ast: List[ast.AST]
    
    def __init__(self, symbol: str, condition_src: str, body_ast: List[ast.AST], original_node: ast.While):
        super().__init__(symbol, original_node, {})
        self.condition_src = condition_src
        self.body_ast = body_ast

@dataclass
class PlaceholderIf(PlaceholderNode):
    """Placeholder per nodi If con body AST preservato."""
    condition_src: str
    body_ast: List[ast.AST]
    orelse_ast: List[ast.AST]
    
    def __init__(self, symbol: str, condition_src: str, body_ast: List[ast.AST], 
                 orelse_ast: List[ast.AST], original_node: ast.If):
        super().__init__(symbol, original_node, {})
        self.condition_src = condition_src
        self.body_ast = body_ast
        self.orelse_ast = orelse_ast

@dataclass
class PlaceholderFunction(PlaceholderNode):
    """Placeholder per nodi Function con body AST preservato."""
    name: str
    args_src: str
    body_ast: List[ast.AST]
    
    def __init__(self, symbol: str, name: str, args_src: str, body_ast: List[ast.AST], 
                 original_node: ast.FunctionDef):
        super().__init__(symbol, original_node, {})
        self.name = name
        self.args_src = args_src
        self.body_ast = body_ast

class NeuroglyphASTCompressor(ast.NodeTransformer):
    """
    AST-based compressor che preserva la struttura AST evitando string compression.
    """
    
    def __init__(self, encoding_level: int = 3):
        self.encoding_level = encoding_level
        self.compressed_symbols = []
        self.payload_registry = {}  # Registro dei payload AST
        self.symbol_counter = 0
        
    def visit_FunctionDef(self, node: ast.FunctionDef) -> ast.AST:
        """Comprimi FunctionDef preservando body AST."""
        # 1. Visita ricorsiva per comprimere sotto-nodi prima
        node = self.generic_visit(node)

        # 2. Estrai metadati
        name = node.name
        args_src = self._extract_args_string(node.args)

        # 3. Preserva body come AST (non come stringa!)
        body_ast = node.body

        # 4. Registra per decompressione
        payload_key = f"func_{self.symbol_counter}"
        self.payload_registry[payload_key] = {
            "type": "function",
            "name": name,
            "args": args_src,
            "body_ast": body_ast,
            "original_node": node
        }
        self.symbol_counter += 1

        # 5. Aggiungi simbolo compresso
        symbol = "⟨⟩"
        symbol_token = f"{symbol}§{payload_key}§"
        self.compressed_symbols.append(symbol_token)

        print(f"   🎯 AST-COMPRESS: Function '{name}' → {symbol_token}")

        # 6. Ritorna nodo placeholder AST-compatibile
        return ast.Expr(value=ast.Constant(value=symbol_token))
    
    def visit_While(self, node: ast.While) -> ast.AST:
        """Comprimi While preservando body AST."""
        # 1. Visita ricorsiva per comprimere sotto-nodi prima
        node = self.generic_visit(node)

        # 2. Estrai condizione come stringa
        condition_src = ast.unparse(node.test)

        # 3. Preserva body come AST (non come stringa!)
        body_ast = node.body

        # 4. Registra per decompressione
        payload_key = f"while_{self.symbol_counter}"
        self.payload_registry[payload_key] = {
            "type": "while",
            "condition": condition_src,
            "body_ast": body_ast,
            "original_node": node
        }
        self.symbol_counter += 1

        # 5. Aggiungi simbolo compresso
        symbol = "⟳"
        symbol_token = f"{symbol}§{payload_key}§"
        self.compressed_symbols.append(symbol_token)

        print(f"   🎯 AST-COMPRESS: While '{condition_src}' → {symbol_token}")

        # 6. Ritorna nodo placeholder AST-compatibile
        return ast.Expr(value=ast.Constant(value=symbol_token))
    
    def visit_If(self, node: ast.If) -> ast.AST:
        """Comprimi If preservando body AST."""
        # 1. Visita ricorsiva per comprimere sotto-nodi prima
        node = self.generic_visit(node)

        # 2. Estrai condizione come stringa
        condition_src = ast.unparse(node.test)

        # 3. Preserva body e orelse come AST
        body_ast = node.body
        orelse_ast = node.orelse

        # 4. Registra per decompressione
        payload_key = f"if_{self.symbol_counter}"
        self.payload_registry[payload_key] = {
            "type": "if",
            "condition": condition_src,
            "body_ast": body_ast,
            "orelse_ast": orelse_ast,
            "original_node": node
        }
        self.symbol_counter += 1

        # 5. Aggiungi simbolo compresso
        symbol = "◊"
        symbol_token = f"{symbol}§{payload_key}§"
        self.compressed_symbols.append(symbol_token)

        print(f"   🎯 AST-COMPRESS: If '{condition_src}' → {symbol_token}")

        # 6. Ritorna nodo placeholder AST-compatibile
        return ast.Expr(value=ast.Constant(value=symbol_token))
    
    def visit_Assign(self, node: ast.Assign) -> ast.AST:
        """Comprimi Assign semplici."""
        # Per ora mantieni Assign semplici senza compressione AST
        # (possono essere gestiti con string-based compression)
        return node
    
    def _extract_args_string(self, args: ast.arguments) -> str:
        """Estrai stringa argomenti da ast.arguments."""
        arg_names = [arg.arg for arg in args.args]
        return ", ".join(arg_names)
    
    def compress_ast(self, source_code: str) -> Dict[str, Any]:
        """
        Comprimi codice sorgente usando AST-based approach.
        
        Args:
            source_code: Codice sorgente da comprimere
            
        Returns:
            Dizionario con risultati compressione AST
        """
        print(f"🎯 AST-COMPRESS: Inizio compressione AST-based")
        
        # 1. Parse AST originale
        original_ast = ast.parse(source_code)
        
        # 2. Applica compressione AST
        compressed_ast = self.visit(original_ast)
        
        # 3. Calcola metriche
        original_length = len(source_code)
        compressed_length = len(' '.join(self.compressed_symbols))
        compression_ratio = (original_length - compressed_length) / original_length * 100
        
        result = {
            "compressed_symbols": self.compressed_symbols,
            "payload_registry": self.payload_registry,
            "compressed_ast": compressed_ast,
            "original_ast": original_ast,
            "compression_ratio": compression_ratio,
            "original_length": original_length,
            "compressed_length": compressed_length
        }
        
        print(f"✅ AST-COMPRESS: Completata")
        print(f"   - Simboli generati: {len(self.compressed_symbols)}")
        print(f"   - Payload registrati: {len(self.payload_registry)}")
        print(f"   - Compressione: {compression_ratio:.1f}%")
        
        return result

class NeuroglyphASTDecompressor:
    """
    AST-based decompressor che ricostruisce AST da simboli preservando fidelity.
    """
    
    def __init__(self, payload_registry: Dict[str, Any]):
        self.payload_registry = payload_registry
    
    def decompress_symbols(self, compressed_symbols: List[str]) -> ast.Module:
        """
        Decomprime simboli in AST preservando struttura originale.
        
        Args:
            compressed_symbols: Lista simboli compressi
            
        Returns:
            AST Module ricostruito
        """
        print(f"🎯 AST-DECOMPRESS: Inizio decompressione AST-based")
        
        # Crea modulo vuoto
        module = ast.Module(body=[], type_ignores=[])

        # Processa ogni simbolo
        for symbol_token in compressed_symbols:
            if "§" in symbol_token:
                # Simbolo con payload
                parts = symbol_token.split("§")
                if len(parts) >= 3:
                    symbol = parts[0]
                    payload_key = parts[1]

                    if payload_key in self.payload_registry:
                        payload = self.payload_registry[payload_key]
                        node = self._reconstruct_node_from_payload(symbol, payload)
                        if node:
                            module.body.append(node)
                            print(f"   🎯 AST-DECOMPRESS: {symbol_token} → {type(node).__name__}")

        # 🎯 FIX: Aggiungi attributi lineno per AST valido
        ast.fix_missing_locations(module)

        print(f"✅ AST-DECOMPRESS: Completata")
        print(f"   - Nodi ricostruiti: {len(module.body)}")

        return module
    
    def _reconstruct_node_from_payload(self, symbol: str, payload: Dict[str, Any]) -> Optional[ast.AST]:
        """Ricostruisce nodo AST da payload preservando struttura originale."""
        
        if payload["type"] == "function":
            # Ricostruisci FunctionDef con body AST originale
            name = payload["name"]
            body_ast = payload["body_ast"]  # AST originale preservato!
            
            # Crea args semplificati
            args = ast.arguments(
                posonlyargs=[], args=[], vararg=None, kwonlyargs=[],
                kw_defaults=[], kwarg=None, defaults=[]
            )
            
            # Aggiungi argomenti se presenti
            if payload["args"]:
                arg_names = [name.strip() for name in payload["args"].split(",") if name.strip()]
                args.args = [ast.arg(arg=name, annotation=None) for name in arg_names]
            
            return ast.FunctionDef(
                name=name,
                args=args,
                body=body_ast,  # 🎯 AST originale preservato!
                decorator_list=[],
                returns=None
            )
        
        elif payload["type"] == "while":
            # Ricostruisci While con body AST originale
            condition_src = payload["condition"]
            body_ast = payload["body_ast"]  # AST originale preservato!
            
            # Parse condizione
            condition_ast = ast.parse(condition_src, mode='eval')
            
            return ast.While(
                test=condition_ast.body,
                body=body_ast,  # 🎯 AST originale preservato!
                orelse=[]
            )
        
        elif payload["type"] == "if":
            # Ricostruisci If con body AST originale
            condition_src = payload["condition"]
            body_ast = payload["body_ast"]  # AST originale preservato!
            orelse_ast = payload["orelse_ast"]  # AST originale preservato!
            
            # Parse condizione
            condition_ast = ast.parse(condition_src, mode='eval')
            
            return ast.If(
                test=condition_ast.body,
                body=body_ast,  # 🎯 AST originale preservato!
                orelse=orelse_ast  # 🎯 AST originale preservato!
            )
        
        return None
