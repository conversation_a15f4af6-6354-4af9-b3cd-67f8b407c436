#!/usr/bin/env python3
"""
NEUROGLYPH Symbol Mapper v2.0

Implementa mappatura bidirezionale tra simboli NEUROGLYPH e strutture AST
per ricostruzione intelligente del codice sorgente.
"""

import ast
import re
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

@dataclass
class ASTNodeTemplate:
    """Template per generazione nodi AST."""
    node_type: type
    required_fields: Dict[str, Any]
    optional_fields: Dict[str, Any]
    placeholder_values: Dict[str, str]

@dataclass
class MappingResult:
    """Risultato della mappatura simboli → AST."""
    ast_structure: ast.AST
    mapping_info: Dict[str, Any]
    placeholders_used: List[str]
    reconstruction_metadata: Dict[str, Any]

class SymbolMapper:
    """
    Mappatore simbolico NEUROGLYPH per conversione bidirezionale
    tra simboli e strutture AST.
    """
    
    def __init__(self):
        """Inizializza il mappatore simbolico."""
        self.symbol_to_ast_map = self._initialize_symbol_mappings()
        self.ast_templates = self._initialize_ast_templates()
        self.placeholder_counter = 0

        # 🎯 PATCH 5: Mapping per stable-sort
        self._symbol_orig_index = {}

        print("🔧 SymbolMapper inizializzato")
        print(f"   - Mappature simbolo→AST: {len(self.symbol_to_ast_map)}")
        print(f"   - Template AST: {len(self.ast_templates)}")
    
    def _initialize_symbol_mappings(self) -> Dict[str, str]:
        """Inizializza mappature simbolo → tipo AST."""
        return {
            # LIVELLO 1 - BASIC
            "⟨⟩": "FunctionDef",
            "◊": "If",
            "☇": "Else",  # 2️⃣ ELSE CLAUSE
            "⟦⟧": "Assign",
            "⊞": "AugAssign",  # 5️⃣ AUGMENTED ASSIGNMENT

            # LIVELLO 2 - INTERMEDIATE
            "⟲": "For",
            "⟳": "While",
            "⟐": "Try",  # 🔧 TRY/EXCEPT SUPPORT
            "⟡": "ClassDef",
            "⟢": "Import",

            # LIVELLO 3 - ADVANCED
            "⊶": "AsyncFunctionDef",
            "⊷": "Await",
            "⟐": "Try",
            "⟔": "ListComp",

            # LIVELLO 4 - EXPERT
            "⟕": "With",
            "⟖": "GeneratorExp",

            # LIVELLO 5 - ULTRA (Pattern complessi)
            "⟈": "IfElseChain",  # Custom pattern
            "⟳◊⟵": "ForIfReturn",  # Custom pattern
            "⊶⟲": "AsyncLoop",  # Custom pattern
            "⟔◊": "ConditionalAssign",  # Conditional assignment
            "⟲⟵": "RecursiveCall",  # Recursive function call

            # FASE 1 - SIMBOLI MANCANTI CRITICI
            "⤴": "Return",        # Return statement
            "⟇": "Try",           # Try block
            "⟈": "Except",        # Except handler
            "⟉": "Finally",       # Finally block
            "⊕": "Add",           # Addition operator
            "⊖": "Sub",           # Subtraction operator
            "⊗": "Mul",           # Multiplication operator
            "⊘": "Div",           # Division operator
            "≡": "Eq",            # Equality operator
            "≠": "NotEq",         # Not equal operator
            "≤": "LtE",           # Less than or equal
            "≥": "GtE",           # Greater than or equal
            "∧": "And",           # Logical AND
            "∨": "Or",            # Logical OR
            "¬": "Not",           # Logical NOT

            # STEP FINALE S-1: TOKEN END
            "⟩": "BlockEnd",      # End of block marker

            # 🛠 PRIORITÀ 1: LIST COMPREHENSION
            "⟔": "ListComp",      # List comprehension
        }
    
    def _initialize_ast_templates(self) -> Dict[str, ASTNodeTemplate]:
        """Inizializza template per generazione nodi AST."""
        templates = {}
        
        # FunctionDef template
        templates["FunctionDef"] = ASTNodeTemplate(
            node_type=ast.FunctionDef,
            required_fields={
                "name": "placeholder_function",
                "args": ast.arguments(
                    posonlyargs=[],
                    args=[],
                    vararg=None,
                    kwonlyargs=[],
                    kw_defaults=[],
                    kwarg=None,
                    defaults=[]
                ),
                "body": [ast.Pass()],
                "decorator_list": [],
                "returns": None
            },
            optional_fields={},
            placeholder_values={
                "name": "func_{counter}"
            }
        )
        
        # If template
        templates["If"] = ASTNodeTemplate(
            node_type=ast.If,
            required_fields={
                "test": ast.Name(id="condition", ctx=ast.Load()),
                "body": [ast.Pass()],
                "orelse": []
            },
            optional_fields={},
            placeholder_values={
                "test": "condition_{counter}"
            }
        )

        # 2️⃣ ELSE TEMPLATE (marker speciale)
        templates["Else"] = ASTNodeTemplate(
            node_type=ast.Pass,  # Placeholder, la logica è gestita separatamente
            required_fields={},
            optional_fields={},
            placeholder_values={}
        )

        # 3️⃣ AUGMENTED ASSIGNMENT TEMPLATE
        templates["AugAssign"] = ASTNodeTemplate(
            node_type=ast.AugAssign,
            required_fields={
                "target": ast.Name(id="variable", ctx=ast.Store()),
                "op": ast.Add(),
                "value": ast.Constant(value=1)
            },
            optional_fields={},
            placeholder_values={
                "target": "var_{counter}",
                "value": "value_{counter}"
            }
        )
        
        # For template
        templates["For"] = ASTNodeTemplate(
            node_type=ast.For,
            required_fields={
                "target": ast.Name(id="item", ctx=ast.Store()),
                "iter": ast.Name(id="iterable", ctx=ast.Load()),
                "body": [ast.Pass()],
                "orelse": []
            },
            optional_fields={},
            placeholder_values={
                "target": "item_{counter}",
                "iter": "iterable_{counter}"
            }
        )
        
        # While template
        templates["While"] = ASTNodeTemplate(
            node_type=ast.While,
            required_fields={
                "test": ast.Name(id="condition", ctx=ast.Load()),
                "body": [ast.Pass()],
                "orelse": []
            },
            optional_fields={},
            placeholder_values={
                "test": "condition_{counter}"
            }
        )
        
        # ClassDef template
        templates["ClassDef"] = ASTNodeTemplate(
            node_type=ast.ClassDef,
            required_fields={
                "name": "PlaceholderClass",
                "bases": [],
                "keywords": [],
                "body": [ast.Pass()],
                "decorator_list": []
            },
            optional_fields={},
            placeholder_values={
                "name": "Class_{counter}"
            }
        )
        
        # Assign template
        templates["Assign"] = ASTNodeTemplate(
            node_type=ast.Assign,
            required_fields={
                "targets": [ast.Name(id="variable", ctx=ast.Store())],
                "value": ast.Constant(value=None)
            },
            optional_fields={},
            placeholder_values={
                "targets": "var_{counter}",
                "value": "value_{counter}"
            }
        )
        
        # AsyncFunctionDef template
        templates["AsyncFunctionDef"] = ASTNodeTemplate(
            node_type=ast.AsyncFunctionDef,
            required_fields={
                "name": "async_function",
                "args": ast.arguments(
                    posonlyargs=[],
                    args=[],
                    vararg=None,
                    kwonlyargs=[],
                    kw_defaults=[],
                    kwarg=None,
                    defaults=[]
                ),
                "body": [ast.Pass()],
                "decorator_list": [],
                "returns": None
            },
            optional_fields={},
            placeholder_values={
                "name": "async_func_{counter}"
            }
        )

        # Await template
        templates["Await"] = ASTNodeTemplate(
            node_type=ast.Await,
            required_fields={
                "value": ast.Name(id="expression", ctx=ast.Load())
            },
            optional_fields={},
            placeholder_values={
                "value": "await_expr_{counter}"
            }
        )
        
        # Try template
        templates["Try"] = ASTNodeTemplate(
            node_type=ast.Try,
            required_fields={
                "body": [ast.Pass()],
                "handlers": [ast.ExceptHandler(
                    type=None,
                    name=None,
                    body=[ast.Pass()]
                )],
                "orelse": [],
                "finalbody": []
            },
            optional_fields={},
            placeholder_values={}
        )
        
        # With template
        templates["With"] = ASTNodeTemplate(
            node_type=ast.With,
            required_fields={
                "items": [ast.withitem(
                    context_expr=ast.Name(id="context", ctx=ast.Load()),
                    optional_vars=None
                )],
                "body": [ast.Pass()]
            },
            optional_fields={},
            placeholder_values={
                "context": "context_{counter}"
            }
        )

        # FASE 1 - TEMPLATE PER SIMBOLI MANCANTI

        # Return template
        templates["Return"] = ASTNodeTemplate(
            node_type=ast.Return,
            required_fields={
                "value": ast.Constant(value=None)
            },
            optional_fields={},
            placeholder_values={
                "value": "return_value_{counter}"
            }
        )

        # Except template
        templates["Except"] = ASTNodeTemplate(
            node_type=ast.ExceptHandler,
            required_fields={
                "type": None,
                "name": None,
                "body": [ast.Pass()]
            },
            optional_fields={},
            placeholder_values={}
        )

        # Finally template (usa Try con finalbody)
        templates["Finally"] = ASTNodeTemplate(
            node_type=ast.Try,
            required_fields={
                "body": [ast.Pass()],
                "handlers": [],
                "orelse": [],
                "finalbody": [ast.Pass()]
            },
            optional_fields={},
            placeholder_values={}
        )

        # Binary operators templates
        templates["Add"] = ASTNodeTemplate(
            node_type=ast.BinOp,
            required_fields={
                "left": ast.Name(id="left", ctx=ast.Load()),
                "op": ast.Add(),
                "right": ast.Name(id="right", ctx=ast.Load())
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        templates["Sub"] = ASTNodeTemplate(
            node_type=ast.BinOp,
            required_fields={
                "left": ast.Name(id="left", ctx=ast.Load()),
                "op": ast.Sub(),
                "right": ast.Name(id="right", ctx=ast.Load())
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        templates["Mul"] = ASTNodeTemplate(
            node_type=ast.BinOp,
            required_fields={
                "left": ast.Name(id="left", ctx=ast.Load()),
                "op": ast.Mult(),
                "right": ast.Name(id="right", ctx=ast.Load())
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        templates["Div"] = ASTNodeTemplate(
            node_type=ast.BinOp,
            required_fields={
                "left": ast.Name(id="left", ctx=ast.Load()),
                "op": ast.Div(),
                "right": ast.Name(id="right", ctx=ast.Load())
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        # Comparison operators templates
        templates["Eq"] = ASTNodeTemplate(
            node_type=ast.Compare,
            required_fields={
                "left": ast.Name(id="left", ctx=ast.Load()),
                "ops": [ast.Eq()],
                "comparators": [ast.Name(id="right", ctx=ast.Load())]
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        templates["NotEq"] = ASTNodeTemplate(
            node_type=ast.Compare,
            required_fields={
                "left": ast.Name(id="left", ctx=ast.Load()),
                "ops": [ast.NotEq()],
                "comparators": [ast.Name(id="right", ctx=ast.Load())]
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        # Boolean operators templates
        templates["And"] = ASTNodeTemplate(
            node_type=ast.BoolOp,
            required_fields={
                "op": ast.And(),
                "values": [
                    ast.Name(id="left", ctx=ast.Load()),
                    ast.Name(id="right", ctx=ast.Load())
                ]
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        templates["Or"] = ASTNodeTemplate(
            node_type=ast.BoolOp,
            required_fields={
                "op": ast.Or(),
                "values": [
                    ast.Name(id="left", ctx=ast.Load()),
                    ast.Name(id="right", ctx=ast.Load())
                ]
            },
            optional_fields={},
            placeholder_values={
                "left": "left_{counter}",
                "right": "right_{counter}"
            }
        )

        templates["Not"] = ASTNodeTemplate(
            node_type=ast.UnaryOp,
            required_fields={
                "op": ast.Not(),
                "operand": ast.Name(id="operand", ctx=ast.Load())
            },
            optional_fields={},
            placeholder_values={
                "operand": "operand_{counter}"
            }
        )

        # S-1: TEMPLATE PER NUOVI SIMBOLI

        # For loop template
        templates["For"] = ASTNodeTemplate(
            node_type=ast.For,
            required_fields={
                "target": ast.Name(id="i", ctx=ast.Store()),
                "iter": ast.Name(id="iterable", ctx=ast.Load()),
                "body": [ast.Pass()],
                "orelse": []
            },
            optional_fields={},
            placeholder_values={
                "target": "i_{counter}",
                "iter": "iterable_{counter}"
            }
        )

        # While loop template
        templates["While"] = ASTNodeTemplate(
            node_type=ast.While,
            required_fields={
                "test": ast.Constant(value=True),
                "body": [ast.Pass()],
                "orelse": []
            },
            optional_fields={},
            placeholder_values={
                "test": "condition_{counter}"
            }
        )

        # List comprehension template
        templates["ListComp"] = ASTNodeTemplate(
            node_type=ast.ListComp,
            required_fields={
                "elt": ast.Name(id="x", ctx=ast.Load()),
                "generators": [ast.comprehension(
                    target=ast.Name(id="x", ctx=ast.Store()),
                    iter=ast.Name(id="iterable", ctx=ast.Load()),
                    ifs=[],
                    is_async=0
                )]
            },
            optional_fields={},
            placeholder_values={
                "elt": "x_{counter}",
                "target": "x_{counter}",
                "iter": "iterable_{counter}"
            }
        )

        # 🎯 PATCH 2: TEMPLATE ULTRA PATTERNS

        # Conditional assignment template (⟔◊)
        templates["ConditionalAssign"] = ASTNodeTemplate(
            node_type=ast.Assign,
            required_fields={
                "targets": [ast.Name(id="variable", ctx=ast.Store())],
                "value": ast.ListComp(
                    elt=ast.Name(id="x", ctx=ast.Load()),
                    generators=[ast.comprehension(
                        target=ast.Name(id="x", ctx=ast.Store()),
                        iter=ast.Name(id="iterable", ctx=ast.Load()),
                        ifs=[],
                        is_async=0
                    )]
                )
            },
            optional_fields={},
            placeholder_values={
                "variable": "var_{counter}",
                "elt": "x_{counter}",
                "target": "x_{counter}",
                "iter": "iterable_{counter}"
            }
        )

        # Recursive call template (⟲⟵)
        templates["RecursiveCall"] = ASTNodeTemplate(
            node_type=ast.Return,
            required_fields={
                "value": ast.BinOp(
                    left=ast.Call(
                        func=ast.Name(id="function", ctx=ast.Load()),
                        args=[ast.Name(id="arg1", ctx=ast.Load())],
                        keywords=[]
                    ),
                    op=ast.Add(),
                    right=ast.Call(
                        func=ast.Name(id="function", ctx=ast.Load()),
                        args=[ast.Name(id="arg2", ctx=ast.Load())],
                        keywords=[]
                    )
                )
            },
            optional_fields={},
            placeholder_values={
                "function": "func_{counter}",
                "arg1": "arg1_{counter}",
                "arg2": "arg2_{counter}"
            }
        )

        return templates

    # 2️⃣ OPERATOR MAPPING PER PARSER ESPRESSIONI
    def _get_operator_map(self) -> Dict[str, ast.operator]:
        """Mappa simboli/operatori a classi AST."""
        return {
            '⊕': ast.Add(),
            '+': ast.Add(),
            '⊖': ast.Sub(),
            '-': ast.Sub(),
            '⊗': ast.Mult(),
            '*': ast.Mult(),
            '⊘': ast.Div(),
            '/': ast.Div(),
            '≡': ast.Eq(),
            '==': ast.Eq(),
            '≠': ast.NotEq(),
            '!=': ast.NotEq(),
            '≤': ast.LtE(),
            '<=': ast.LtE(),
            '≥': ast.GtE(),
            '>=': ast.GtE(),
            '∧': ast.And(),
            '∨': ast.Or(),
            '¬': ast.Not()
        }

    def _generate_placeholder_name(self, base_name: str) -> str:
        """Genera nome placeholder unico."""
        self.placeholder_counter += 1
        return base_name.format(counter=self.placeholder_counter)

    def _decode_payload_recursively(self, payload: str, compressed_symbols: List[str],
                                   payload_list: List[str], symbol_metadata: Dict) -> str:
        """Decodifica ricorsivamente simboli nei payload."""
        # Pattern per simboli con payload
        pattern = r"[⟔⟲⟳⟡⊗⊕◊⟦⟧⤴⟨⟩][^\s]*§\d+§"
        max_iterations = 5  # Evita loop infiniti
        iteration = 0

        while re.search(pattern, payload) and iteration < max_iterations:
            parts = payload.split()
            new_parts = []
            for part in parts:
                if re.fullmatch(pattern, part):
                    # È un simbolo con payload, cerca di decodificarlo
                    if part in symbol_metadata:
                        # Usa il payload originale del simbolo
                        original_text = symbol_metadata[part].get("original_text", part)
                        new_parts.append(original_text)
                        print(f"   Decodificato ricorsivamente: {part} → {original_text}")
                    else:
                        # Prova a estrarre dall'indice payload
                        try:
                            base_symbol, payload_idx_str, _ = part.split("§")
                            payload_idx = int(payload_idx_str)
                            if payload_idx < len(payload_list):
                                original_text = payload_list[payload_idx]
                                new_parts.append(original_text)
                                print(f"   Decodificato da payload: {part} → {original_text}")
                            else:
                                new_parts.append(part)
                        except (ValueError, IndexError):
                            new_parts.append(part)
                else:
                    new_parts.append(part)
            payload = " ".join(new_parts)
            iteration += 1

        return payload

    # 2️⃣ RICORSIONE PAYLOAD SICURA - PATCH STABILE
    _MAX_DEPTH = 8
    _RX = re.compile(r"([⟔⟲⟳⟡⊗⊕◊⟦⟧⤴⟨⟩☇⊞])[^§]*§(\d+)§")

    def _strip_all_symbols(self, text: str, registry: dict, depth: int = 0) -> str:
        """Ricorsione payload sicura con guardie anti-loop."""
        if depth >= self._MAX_DEPTH:
            return text                      # fermo la ricorsione

        m = self._RX.search(text)
        if not m:
            return text

        sym = f"{m.group(1)}§{m.group(2)}§"
        repl = registry.get(sym, {}).get("payload_value", sym)

        if sym == repl:                      # chiave mancante → evito loop
            return text

        return self._strip_all_symbols(text.replace(sym, repl), registry, depth + 1)

    def _create_ast_node(self, symbol: str, metadata: Optional[Dict] = None) -> ast.AST:
        """
        Crea nodo AST da simbolo NEUROGLYPH.
        
        Args:
            symbol: Simbolo NEUROGLYPH
            metadata: Metadati aggiuntivi per la creazione
            
        Returns:
            Nodo AST corrispondente
        """
        if symbol not in self.symbol_to_ast_map:
            # Simbolo sconosciuto - crea nodo generico
            return ast.Expr(value=ast.Constant(value=symbol))
        
        ast_type = self.symbol_to_ast_map[symbol]
        
        if ast_type not in self.ast_templates:
            # Template non disponibile - nodo generico
            return ast.Expr(value=ast.Constant(value=f"UNKNOWN_{symbol}"))
        
        template = self.ast_templates[ast_type]
        
        # Crea nodo con placeholder
        node_fields = template.required_fields.copy()
        
        # Sostituisci placeholder con nomi generati
        for field_name, placeholder_pattern in template.placeholder_values.items():
            if field_name in node_fields:
                if isinstance(node_fields[field_name], str):
                    node_fields[field_name] = self._generate_placeholder_name(placeholder_pattern)
                elif isinstance(node_fields[field_name], ast.Name):
                    node_fields[field_name].id = self._generate_placeholder_name(placeholder_pattern)
        
        # Crea nodo AST
        node = template.node_type(**node_fields)
        
        return node

    def _create_ast_node_with_payload(self,
                                     symbol: str,
                                     payload_value: Any,
                                     symbol_metadata: Dict,
                                     compression_metadata: Dict) -> ast.AST:
        """
        Crea nodo AST da simbolo NEUROGLYPH con payload.

        Args:
            symbol: Simbolo NEUROGLYPH base
            payload_value: Valore dal payload
            symbol_metadata: Metadati specifici del simbolo
            compression_metadata: Metadati di compressione

        Returns:
            Nodo AST con dati reali dal payload
        """
        if symbol not in self.symbol_to_ast_map:
            # Simbolo sconosciuto - crea nodo generico
            return ast.Expr(value=ast.Constant(value=f"UNKNOWN_{symbol}_{payload_value}"))

        ast_type = self.symbol_to_ast_map[symbol]

        # FASE 2 - CREAZIONE NODI CON PAYLOAD REALE

        if ast_type == "FunctionDef":
            # 🎯 PATCH 4: Funzione con nome e argomenti dal payload
            if payload_value and ":" in payload_value:
                func_name, args_str = payload_value.split(":", 1)
                func_name = func_name.strip()
                args_str = args_str.strip()

                print(f"   🎯 PATCH 4: Creating function '{func_name}' with args '{args_str}'")

                # Parse argomenti se presenti
                if args_str:
                    try:
                        # Crea una funzione temporanea per parsare gli argomenti
                        temp_func_code = f"def _({args_str}): pass"
                        temp_ast = ast.parse(temp_func_code)
                        parsed_args = temp_ast.body[0].args
                        print(f"   🎯 PATCH 4: Arguments parsed successfully")
                    except Exception as e:
                        print(f"   🎯 PATCH 4: Error parsing args '{args_str}': {e}")
                        # Fallback: nessun argomento
                        parsed_args = ast.arguments(
                            posonlyargs=[], args=[], vararg=None,
                            kwonlyargs=[], kw_defaults=[], kwarg=None, defaults=[]
                        )
                else:
                    # Nessun argomento
                    parsed_args = ast.arguments(
                        posonlyargs=[], args=[], vararg=None,
                        kwonlyargs=[], kw_defaults=[], kwarg=None, defaults=[]
                    )
            else:
                # Formato legacy: solo nome
                func_name = payload_value if payload_value else f"func_{self.placeholder_counter}"
                parsed_args = ast.arguments(
                    posonlyargs=[], args=[], vararg=None,
                    kwonlyargs=[], kw_defaults=[], kwarg=None, defaults=[]
                )

            node = ast.FunctionDef(
                name=func_name,
                args=parsed_args,
                body=[ast.Pass()],  # Verrà popolato in fase di ricostruzione
                decorator_list=[],
                returns=None
            )

            return node

        elif ast_type == "Return":
            # Return con valore reale dal payload
            if payload_value:
                # Prova a parsare il valore
                if payload_value.startswith('"') and payload_value.endswith('"'):
                    # Stringa
                    value = ast.Constant(value=payload_value[1:-1])
                elif payload_value.startswith("'") and payload_value.endswith("'"):
                    # Stringa con apici singoli
                    value = ast.Constant(value=payload_value[1:-1])
                elif payload_value.replace('.', '').replace('-', '').isdigit():
                    # Numero
                    try:
                        num_value = int(payload_value) if '.' not in payload_value else float(payload_value)
                        value = ast.Constant(value=num_value)
                    except ValueError:
                        value = ast.Constant(value=payload_value)
                else:
                    # Identificatore o espressione
                    value = ast.Name(id=payload_value, ctx=ast.Load())
            else:
                value = ast.Constant(value=None)

            return ast.Return(value=value)

        elif ast_type == "Assign":
            # 🔧 PATCH LISTCOMP: Riconosci e costruisci ListComp
            if payload_value and "=" in payload_value:
                rhs_code = payload_value.split("=", 1)[1].strip()

                # Se contiene " for " e " in " (list comprehension senza parentesi)
                if " for " in rhs_code and " in " in rhs_code:
                    # Compila in AST la list-comprehension con parentesi aggiunte
                    try:
                        # Aggiungi parentesi quadre se mancanti
                        if not rhs_code.startswith("["):
                            rhs_code = f"[{rhs_code}]"
                        list_comp_node = ast.parse(rhs_code, mode="eval").body
                        target = ast.Name(id=payload_value.split("=",1)[0].strip(), ctx=ast.Store())
                        assign_node = ast.Assign(targets=[target], value=list_comp_node)
                        return assign_node
                    except SyntaxError:
                        # Fallback al processing normale se parsing fallisce
                        pass

                # Processing normale per altri assignment
                var_name, var_value = payload_value.split("=", 1)
                var_name = var_name.strip()
                var_value = var_value.strip()

                # Crea target
                target = ast.Name(id=var_name, ctx=ast.Store())

                # 2️⃣ GESTIONE SIMBOLI NEL VALORE
                if var_value.startswith('⊗§') and '§' in var_value:
                    # Simbolo operatore - cerca nei metadati globali
                    global_symbol_metadata = compression_metadata.get("symbol_metadata", {})
                    symbol_meta = global_symbol_metadata.get(var_value, {})

                    if symbol_meta.get("payload_value"):
                        # Usa il payload originale dell'operatore
                        original_expr = symbol_meta["payload_value"]

                        # Se è un'espressione binaria, crea BinOp
                        if " * " in original_expr:
                            parts = original_expr.split(" * ", 1)
                            if len(parts) == 2:
                                left = self._parse_value_from_payload(parts[0].strip())
                                right = self._parse_value_from_payload(parts[1].strip())
                                value = ast.BinOp(left=left, op=ast.Mult(), right=right)
                            else:
                                value = self._parse_value_from_payload(original_expr)
                        elif " + " in original_expr:
                            parts = original_expr.split(" + ", 1)
                            if len(parts) == 2:
                                left = self._parse_value_from_payload(parts[0].strip())
                                right = self._parse_value_from_payload(parts[1].strip())
                                value = ast.BinOp(left=left, op=ast.Add(), right=right)
                            else:
                                value = self._parse_value_from_payload(original_expr)
                        else:
                            value = self._parse_value_from_payload(original_expr)
                    else:
                        value = ast.Constant(value=var_value)

                # Crea valore normale
                elif var_value.startswith('"') and var_value.endswith('"'):
                    value = ast.Constant(value=var_value[1:-1])
                elif var_value.replace('.', '').replace('-', '').isdigit():
                    try:
                        num_value = int(var_value) if '.' not in var_value else float(var_value)
                        value = ast.Constant(value=num_value)
                    except ValueError:
                        value = ast.Constant(value=var_value)
                else:
                    value = ast.Name(id=var_value, ctx=ast.Load())

                # 1️⃣ LISTCOMP DENTRO ASSIGN: Filtra Expr(ListComp) duplicato
                assign_node = ast.Assign(targets=[target], value=value)

                # Se il valore è una ListComp, rimuovi eventuale Expr(ListComp) precedente
                if isinstance(value, ast.ListComp):
                    # Accedi al body corrente tramite compression_metadata
                    # Questo sarà gestito nel decoder principale
                    pass

                return assign_node
            else:
                # Fallback
                return ast.Assign(
                    targets=[ast.Name(id=f"var_{self.placeholder_counter}", ctx=ast.Store())],
                    value=ast.Constant(value=payload_value)
                )

        elif ast_type == "If":
            # If con condizione reale
            condition_text = payload_value if payload_value else "True"

            # Prova a creare condizione semplice
            if condition_text in ["True", "False"]:
                test = ast.Constant(value=condition_text == "True")
            else:
                # Condizione come nome/espressione
                test = ast.Name(id=condition_text, ctx=ast.Load())

            return ast.If(
                test=test,
                body=[ast.Pass()],
                orelse=[]
            )

        # S-1: GESTIONE NUOVI SIMBOLI CON PAYLOAD
        elif ast_type == "For":
            # For loop con payload reale
            if payload_value and " in " in payload_value:
                var_name, iterable = payload_value.split(" in ", 1)
                var_name = var_name.strip()
                iterable = iterable.strip()

                # Crea target
                target = ast.Name(id=var_name, ctx=ast.Store())

                # Crea iterabile
                if iterable.startswith('[') and iterable.endswith(']'):
                    # Lista letterale
                    iter_node = ast.List(elts=[], ctx=ast.Load())
                elif iterable.replace('.', '').replace('(', '').replace(')', '').isalnum():
                    # Nome variabile
                    iter_node = ast.Name(id=iterable, ctx=ast.Load())
                else:
                    # Espressione generica
                    iter_node = ast.Name(id=iterable, ctx=ast.Load())

                return ast.For(
                    target=target,
                    iter=iter_node,
                    body=[ast.Pass()],
                    orelse=[]
                )
            else:
                # Fallback
                return ast.For(
                    target=ast.Name(id=f"i_{self.placeholder_counter}", ctx=ast.Store()),
                    iter=ast.Name(id=f"iterable_{self.placeholder_counter}", ctx=ast.Load()),
                    body=[ast.Pass()],
                    orelse=[]
                )

        elif ast_type == "While":
            # While loop con condizione reale
            condition_text = payload_value if payload_value else "True"

            # Prova a creare condizione semplice
            if condition_text in ["True", "False"]:
                test = ast.Constant(value=condition_text == "True")
            else:
                # Condizione come nome/espressione
                test = ast.Name(id=condition_text, ctx=ast.Load())

            return ast.While(
                test=test,
                body=[ast.Pass()],
                orelse=[]
            )

        elif ast_type == "Try":
            # 🔧 TRY/EXCEPT/FINALLY SUPPORT - PATCH COMPLETA CON DECOMPRESSIONE
            import base64
            import zlib

            if not payload_value:
                # Fallback per try vuoto
                return ast.Try(body=[ast.Pass()], handlers=[], orelse=[], finalbody=[])

            # Split payload: flags|section1|section2|...
            parts = payload_value.split('|')
            flags_str = parts[0] if parts else ""
            sections = parts[1:] if len(parts) > 1 else []

            try_node = ast.Try(body=[], handlers=[], orelse=[], finalbody=[])
            section_idx = 0

            # Decomprime e ricostruisce il body del try (sempre presente)
            if section_idx < len(sections):
                try:
                    compressed_body = sections[section_idx]
                    decompressed = zlib.decompress(base64.b64decode(compressed_body)).decode()
                    # Rimuovi indentazione esistente e aggiungi quella corretta
                    lines = [line.strip() for line in decompressed.split('\n') if line.strip()]
                    indented_code = '\n'.join(lines)
                    body_ast = ast.parse(indented_code, mode='exec')
                    try_node.body = body_ast.body if body_ast.body else [ast.Pass()]
                    section_idx += 1
                except Exception as e:
                    print(f"   DEBUG: Errore parsing try body: {e}")
                    auto_pass = ast.Pass()
                    auto_pass._ng_auto = True
                    try_node.body = [auto_pass]
            else:
                auto_pass = ast.Pass()
                auto_pass._ng_auto = True
                try_node.body = [auto_pass]

            # Aggiungi handler se ha except
            if "e" in flags_str and section_idx < len(sections):
                try:
                    compressed_except = sections[section_idx]
                    decompressed = zlib.decompress(base64.b64decode(compressed_except)).decode()
                    # Rimuovi indentazione esistente e aggiungi quella corretta
                    lines = [line.strip() for line in decompressed.split('\n') if line.strip()]
                    indented_code = '\n'.join(lines)
                    except_ast = ast.parse(indented_code, mode='exec')
                    handler = ast.ExceptHandler(
                        type=None,  # Catch-all exception
                        name=None,
                        body=except_ast.body if except_ast.body else [ast.Pass()]
                    )
                    try_node.handlers.append(handler)
                    section_idx += 1
                except Exception as e:
                    print(f"   DEBUG: Errore parsing except body: {e}")
                    auto_pass = ast.Pass()
                    auto_pass._ng_auto = True
                    handler = ast.ExceptHandler(type=None, name=None, body=[auto_pass])
                    try_node.handlers.append(handler)

            # Aggiungi finalbody se ha finally
            if "f" in flags_str and section_idx < len(sections):
                try:
                    compressed_finally = sections[section_idx]
                    decompressed = zlib.decompress(base64.b64decode(compressed_finally)).decode()
                    # Rimuovi indentazione esistente e aggiungi quella corretta
                    lines = [line.strip() for line in decompressed.split('\n') if line.strip()]
                    indented_code = '\n'.join(lines)
                    finally_ast = ast.parse(indented_code, mode='exec')
                    try_node.finalbody = finally_ast.body if finally_ast.body else []
                except Exception as e:
                    print(f"   DEBUG: Errore parsing finally body: {e}")
                    try_node.finalbody = []
            else:
                try_node.finalbody = []

            return try_node

        elif ast_type == "ListComp":
            # 🔧 LIST COMPREHENSION FIX - Patch minimale per assignment
            if payload_value:
                # Payload es. "x for x in items if x > 0"
                expr_src = payload_value.strip()
                try:
                    # Costruisci un AST di **ListComp** partendo da sorgente con parentesi
                    lc_ast = ast.parse(f"[{expr_src}]", mode='eval').body
                    return lc_ast
                except Exception as e:
                    print(f"   DEBUG: Errore parsing list comprehension: {e}")
                    # Fallback con parsing manuale
                    pass

            # Fallback
            return ast.ListComp(
                elt=ast.Name(id=f"x_{self.placeholder_counter}", ctx=ast.Load()),
                generators=[ast.comprehension(
                    target=ast.Name(id=f"x_{self.placeholder_counter}", ctx=ast.Store()),
                    iter=ast.Name(id=f"iterable_{self.placeholder_counter}", ctx=ast.Load()),
                    ifs=[],
                    is_async=0
                )]
            )

        # S-1: GESTIONE OPERATORI BINARI CON PAYLOAD
        elif ast_type in ["Add", "Sub", "Mul", "Div"] or symbol in ["⊕", "⊖", "⊗", "⊘"]:
            # Operatori binari con payload
            if payload_value:
                # Parse operatori diversi
                operators = [" * ", " + ", " - ", " / "]
                op_symbols = ["*", "+", "-", "/"]

                for op_str, op_sym in zip(operators, op_symbols):
                    if op_str in payload_value:
                        parts = payload_value.split(op_str, 1)
                        if len(parts) == 2:
                            left = parts[0].strip()
                            right = parts[1].strip()

                            # Crea operandi
                            if left.replace('_', '').isalnum():
                                left_node = ast.Name(id=left, ctx=ast.Load())
                            else:
                                left_node = ast.Constant(value=left)

                            if right.replace('.', '').isdigit():
                                right_node = ast.Constant(value=int(right) if '.' not in right else float(right))
                            elif right.replace('_', '').isalnum():
                                right_node = ast.Name(id=right, ctx=ast.Load())
                            else:
                                right_node = ast.Constant(value=right)

                            # Mappa operatore
                            op_map = {
                                "*": ast.Mult(),
                                "+": ast.Add(),
                                "-": ast.Sub(),
                                "/": ast.Div()
                            }

                            return ast.BinOp(
                                left=left_node,
                                op=op_map[op_sym],
                                right=right_node
                            )

            # Fallback per operatori senza payload valido
            symbol_to_op = {
                "⊕": ast.Add(),
                "⊖": ast.Sub(),
                "⊗": ast.Mult(),
                "⊘": ast.Div(),
                "Add": ast.Add(),
                "Sub": ast.Sub(),
                "Mul": ast.Mult(),
                "Div": ast.Div()
            }

            op_class = symbol_to_op.get(symbol) or symbol_to_op.get(ast_type, ast.Add())

            return ast.BinOp(
                left=ast.Name(id=f"left_{self.placeholder_counter}", ctx=ast.Load()),
                op=op_class,
                right=ast.Name(id=f"right_{self.placeholder_counter}", ctx=ast.Load())
            )



        # PATCH IMMEDIATA: BINARY_ADD (⊕)
        elif symbol.startswith('⊕') or ast_type == "Add":
            # Binary add con payload
            if payload_value:
                # Parse operatori diversi
                operators = [" + ", " - ", " * ", " / "]
                op_symbols = ["+", "-", "*", "/"]

                for op_str, op_sym in zip(operators, op_symbols):
                    if op_str in payload_value:
                        parts = payload_value.split(op_str, 1)
                        if len(parts) == 2:
                            left = self._parse_value_from_payload(parts[0].strip())
                            right = self._parse_value_from_payload(parts[1].strip())

                            # Mappa operatore
                            op_map = {
                                "+": ast.Add(),
                                "-": ast.Sub(),
                                "*": ast.Mult(),
                                "/": ast.Div()
                            }

                            return ast.BinOp(
                                left=left,
                                op=op_map[op_sym],
                                right=right
                            )

            # Fallback per Add senza payload valido
            return ast.BinOp(
                left=ast.Name(id=f"left_{self.placeholder_counter}", ctx=ast.Load()),
                op=ast.Add(),
                right=ast.Name(id=f"right_{self.placeholder_counter}", ctx=ast.Load())
            )

        # 5️⃣ AUGMENTED ASSIGNMENT (⊞) - PATCH STABILE
        elif symbol.startswith('⊞') or ast_type == "AugAssign":
            if payload_value:
                # Parse payload: "target += value"
                op_map = {"+=": ast.Add, "-=": ast.Sub, "*=": ast.Mult, "/=": ast.Div}
                for op_str, op_class in op_map.items():
                    if op_str in payload_value:
                        parts = payload_value.split(op_str, 1)
                        if len(parts) == 2:
                            target = ast.Name(id=parts[0].strip(), ctx=ast.Store())
                            value = self._parse_value_from_payload(parts[1].strip())
                            return ast.AugAssign(target=target, op=op_class(), value=value)

            # Fallback
            return ast.AugAssign(
                target=ast.Name(id=f"var_{self.placeholder_counter}", ctx=ast.Store()),
                op=ast.Add(),
                value=ast.Constant(value=1)
            )

        # 🔧 ASYNC FUNCTION SUPPORT
        elif ast_type == "AsyncFunctionDef":
            # AsyncFunctionDef con payload: name:args|compressed_body
            import base64
            import zlib

            if payload_value and "|" in payload_value:
                parts = payload_value.split("|", 1)
                name_args = parts[0]
                compressed_body = parts[1] if len(parts) > 1 else ""

                # Parse name e args
                if ":" in name_args:
                    name, args = name_args.split(":", 1)
                else:
                    name = name_args
                    args = ""

                # Crea arguments
                if args.strip():
                    # Parse argomenti semplici (separati da virgola)
                    arg_names = [arg.strip() for arg in args.split(",") if arg.strip()]
                    arguments = ast.arguments(
                        posonlyargs=[],
                        args=[ast.arg(arg=arg_name, annotation=None) for arg_name in arg_names],
                        vararg=None,
                        kwonlyargs=[],
                        kw_defaults=[],
                        kwarg=None,
                        defaults=[]
                    )
                else:
                    arguments = ast.arguments(
                        posonlyargs=[], args=[], vararg=None,
                        kwonlyargs=[], kw_defaults=[], kwarg=None, defaults=[]
                    )

                # Decomprime body
                body = []
                if compressed_body:
                    try:
                        decompressed = zlib.decompress(base64.b64decode(compressed_body)).decode()
                        lines = [line.strip() for line in decompressed.split('\n') if line.strip()]
                        body_code = '\n'.join(lines)
                        body_ast = ast.parse(body_code, mode='exec')
                        body = body_ast.body if body_ast.body else [ast.Pass()]
                    except Exception as e:
                        print(f"   DEBUG: Errore decompressione async body: {e}")
                        body = [ast.Pass()]

                return ast.AsyncFunctionDef(
                    name=name,
                    args=arguments,
                    body=body,
                    decorator_list=[],
                    returns=None
                )

            # Fallback
            return ast.AsyncFunctionDef(
                name=f"async_func_{self.placeholder_counter}",
                args=ast.arguments(
                    posonlyargs=[], args=[], vararg=None,
                    kwonlyargs=[], kw_defaults=[], kwarg=None, defaults=[]
                ),
                body=[ast.Pass()],
                decorator_list=[],
                returns=None
            )

        elif ast_type == "Await":
            # Await expression con payload
            if payload_value:
                # Parse espressione await
                try:
                    # Prova a parsare come espressione
                    expr_ast = ast.parse(payload_value, mode='eval')
                    return ast.Await(value=expr_ast.body)
                except:
                    # Fallback: tratta come nome
                    return ast.Await(value=ast.Name(id=payload_value, ctx=ast.Load()))

            # Fallback
            return ast.Await(value=ast.Name(id=f"expr_{self.placeholder_counter}", ctx=ast.Load()))

        # 🎯 PATCH 2: GESTIONE SIMBOLI ULTRA
        elif ast_type == "ConditionalAssign":
            # Conditional assignment: var = [x for x in arr if condition]
            if payload_value:
                # Il payload dovrebbe contenere l'assignment completo
                # Es: "left = [x for x in arr if x < pivot]"
                if "=" in payload_value:
                    var_name, list_comp_expr = payload_value.split("=", 1)
                    var_name = var_name.strip()
                    list_comp_expr = list_comp_expr.strip()

                    try:
                        # Parse la list comprehension
                        if not list_comp_expr.startswith("["):
                            list_comp_expr = f"[{list_comp_expr}]"
                        list_comp_ast = ast.parse(list_comp_expr, mode='eval').body

                        return ast.Assign(
                            targets=[ast.Name(id=var_name, ctx=ast.Store())],
                            value=list_comp_ast
                        )
                    except Exception as e:
                        print(f"   DEBUG: Errore parsing conditional assignment: {e}")
                        # Fallback
                        return ast.Assign(
                            targets=[ast.Name(id=var_name, ctx=ast.Store())],
                            value=ast.List(elts=[], ctx=ast.Load())
                        )

            # Fallback
            return ast.Assign(
                targets=[ast.Name(id=f"var_{self.placeholder_counter}", ctx=ast.Store())],
                value=ast.List(elts=[], ctx=ast.Load())
            )

        elif ast_type == "RecursiveCall":
            # 🎯 PATCH 3: Recursive function call completo
            if payload_value:
                # Il payload dovrebbe contenere la chiamata ricorsiva
                # Es: "return quicksort(left) + middle + quicksort(right)"
                try:
                    # Estrai l'espressione dopo "return"
                    if payload_value.startswith("return "):
                        expr_src = payload_value[7:].strip()  # Rimuovi "return "
                    else:
                        expr_src = payload_value.strip()

                    print(f"   🎯 PATCH 3: Parsing recursive expression: {expr_src}")

                    # Parse come espressione
                    expr_ast = ast.parse(expr_src, mode='eval')
                    return_node = ast.Return(value=expr_ast.body)

                    print(f"   🎯 PATCH 3: Recursive call parsed successfully")
                    return return_node

                except Exception as e:
                    print(f"   🎯 PATCH 3: Errore parsing recursive call: {e}")
                    print(f"   🎯 PATCH 3: Payload problematico: '{payload_value}'")
                    # Fallback: return None
                    return ast.Return(value=ast.Constant(value=None))

            # Fallback
            return ast.Return(value=ast.Constant(value=None))

        # Per altri tipi, usa il metodo standard
        return self._create_ast_node(symbol)

    def _create_ast_node_from_symbol_metadata(self, symbol: str, symbol_metadata: Dict) -> ast.AST:
        """
        Crea nodo AST da simbolo usando i metadati disponibili.

        Args:
            symbol: Simbolo NEUROGLYPH
            symbol_metadata: Metadati del simbolo

        Returns:
            Nodo AST appropriato
        """
        if symbol not in self.symbol_to_ast_map:
            return ast.Expr(value=ast.Constant(value=f"UNKNOWN_{symbol}"))

        ast_type = self.symbol_to_ast_map[symbol]

        # S-1: GESTIONE OPERATORI BINARI SENZA PAYLOAD
        if symbol in ["⊕", "⊖", "⊗", "⊘"]:
            # Usa metadati se disponibili
            if symbol_metadata and "left_operand" in symbol_metadata:
                left = symbol_metadata["left_operand"]
                right = symbol_metadata["right_operand"]
                operator = symbol_metadata["operator"]
            else:
                # Fallback con placeholder
                left = f"left_{self.placeholder_counter}"
                right = f"right_{self.placeholder_counter}"
                operator = {"⊕": "+", "⊖": "-", "⊗": "*", "⊘": "/"}[symbol]
                self.placeholder_counter += 1

            # Crea operatore AST
            op_map = {
                "+": ast.Add(),
                "-": ast.Sub(),
                "*": ast.Mult(),
                "/": ast.Div()
            }

            return ast.BinOp(
                left=ast.Name(id=left, ctx=ast.Load()),
                op=op_map[operator],
                right=ast.Name(id=right, ctx=ast.Load())
            )

        # Per altri simboli, usa il metodo standard
        return self._create_ast_node(symbol)

    # 2️⃣ METODI HELPER PER PARSER ESPRESSIONI
    def _reduce_expression_stack(self, expr_stack: List[ast.expr], op_stack: List[str]) -> Optional[ast.expr]:
        """
        Riduce stack di espressioni e operatori in un singolo nodo AST.

        Args:
            expr_stack: Stack di espressioni
            op_stack: Stack di operatori

        Returns:
            Nodo AST ridotto o None
        """
        if not expr_stack:
            return None

        if len(expr_stack) == 1 and not op_stack:
            return expr_stack[0]

        # Semplice riduzione left-to-right per ora
        if len(expr_stack) >= 2 and op_stack:
            left = expr_stack[0]
            right = expr_stack[1]
            op_symbol = op_stack[0]

            # Ottieni operatore AST
            operator_map = self._get_operator_map()
            base_op = op_symbol.split('§')[0] if '§' in op_symbol else op_symbol

            if base_op in operator_map:
                op_ast = operator_map[base_op]

                # Crea BinOp
                if isinstance(op_ast, (ast.Add, ast.Sub, ast.Mult, ast.Div)):
                    return ast.BinOp(left=left, op=op_ast, right=right)
                elif isinstance(op_ast, (ast.Eq, ast.NotEq, ast.Lt, ast.Gt, ast.LtE, ast.GtE)):
                    return ast.Compare(left=left, ops=[op_ast], comparators=[right])

        # Fallback: restituisci prima espressione
        return expr_stack[0] if expr_stack else None

    def _integrate_expression_in_statement(self, statement: ast.AST, expression: ast.expr) -> ast.AST:
        """
        Integra un'espressione in uno statement.

        Args:
            statement: Statement AST
            expression: Espressione da integrare

        Returns:
            Statement modificato
        """
        # 3️⃣ INTEGRAZIONE LISTCOMP/BINOP IN ASSIGN/RETURN
        if isinstance(statement, ast.Assign):
            # Sostituisci valore con espressione
            statement.value = expression
            return statement

        elif isinstance(statement, ast.Return):
            # Sostituisci valore di return
            statement.value = expression
            return statement

        elif isinstance(statement, (ast.FunctionDef, ast.For, ast.While, ast.If)):
            # Aggiungi espressione al corpo
            if hasattr(statement, 'body') and statement.body:
                if isinstance(statement.body[0], ast.Pass):
                    # Sostituisci pass con espressione
                    statement.body[0] = ast.Expr(value=expression)
                else:
                    # Aggiungi all'inizio del corpo
                    statement.body.insert(0, ast.Expr(value=expression))
            return statement

        # Fallback: restituisci statement originale
        return statement

    def _find_appropriate_function_body(self, statement_node: ast.AST, body_stack: List, metadata: Dict) -> Optional[List]:
        """
        Trova il body della funzione più appropriata per uno statement.

        Args:
            statement_node: Nodo statement (Assign, Return, etc.)
            body_stack: Stack dei body correnti
            metadata: Metadati del simbolo

        Returns:
            Body della funzione target o None se non trovato
        """
        payload_value = metadata.get("payload_value", "")

        # 🎯 PATCH 2: DISTRIBUZIONE STATEMENT INTELLIGENTE

        # 1️⃣ STRATEGIA GENERALE: Evita blocchi di controllo (If, For, While)
        # Se siamo dentro un blocco di controllo, cerca la funzione genitore
        if len(body_stack) >= 3:  # module -> function -> control_block
            # Controlla se il body corrente appartiene a un blocco di controllo
            current_body = body_stack[-1]
            parent_body = body_stack[-2] if len(body_stack) >= 2 else None

            # Se il parent body contiene If/For/While, probabilmente siamo in un blocco di controllo
            if parent_body:
                # Cerca l'ultimo nodo nel parent body
                if parent_body and isinstance(parent_body[-1], (ast.If, ast.For, ast.While, ast.Try)):
                    print(f"   🎯 Rilevato blocco di controllo, cerco funzione genitore")
                    # Cerca la funzione genitore (livello -3 o superiore)
                    for level in range(len(body_stack) - 3, -1, -1):
                        if level >= 0 and body_stack[level]:
                            # Cerca l'ultima funzione in questo livello
                            for node in reversed(body_stack[level]):
                                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                                    print(f"   🎯 Trovata funzione genitore: {node.name}")
                                    return node.body

        # 2️⃣ STRATEGIA SPECIFICA: Return statements vanno sempre nella funzione più vicina
        if isinstance(statement_node, ast.Return):
            # Cerca la funzione più vicina nello stack (dal più profondo al più superficiale)
            for level in range(len(body_stack) - 1, -1, -1):
                if level >= 0 and body_stack[level]:
                    for node in reversed(body_stack[level]):
                        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                            print(f"   🎯 Return assegnato a funzione: {node.name}")
                            return node.body

        # 3️⃣ STRATEGIA ASSIGNMENT: Variabili locali vanno nella funzione corrente
        if isinstance(statement_node, ast.Assign):
            # Se è un assignment di variabile locale (non self.x), va nella funzione
            if payload_value and "=" in payload_value:
                var_name = payload_value.split("=")[0].strip()
                if not var_name.startswith("self."):
                    # Cerca la funzione più vicina
                    for level in range(len(body_stack) - 1, -1, -1):
                        if level >= 0 and body_stack[level]:
                            for node in reversed(body_stack[level]):
                                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                                    print(f"   🎯 Assignment locale '{var_name}' assegnato a funzione: {node.name}")
                                    return node.body

        # 4️⃣ STRATEGIA LEGACY: Mantieni logica specifica per classi esistenti


        # Se è un assignment di inizializzazione, va in __init__
        if isinstance(statement_node, ast.Assign) and (("x" in payload_value and "0" in payload_value) or ("result" in payload_value and "0" in payload_value)):
            # Cerca __init__ nel body della classe (livello 1 del body_stack)
            if len(body_stack) >= 2:
                class_body = body_stack[1]  # Body della classe
                for node in class_body:
                    if isinstance(node, ast.FunctionDef) and node.name == "__init__":
                        return node.body

        # Se è un AugAssign con +=, va in add
        elif isinstance(statement_node, ast.AugAssign) and "+=" in payload_value:
            if len(body_stack) >= 2:
                class_body = body_stack[1]  # Body della classe
                for node in class_body:
                    if isinstance(node, ast.FunctionDef) and node.name == "add":
                        return node.body

        # Se è un AugAssign con *=, va in multiply
        elif isinstance(statement_node, ast.AugAssign) and "*=" in payload_value:
            if len(body_stack) >= 2:
                class_body = body_stack[1]  # Body della classe
                for node in class_body:
                    if isinstance(node, ast.FunctionDef) and node.name == "multiply":
                        return node.body

        # Se è un return con simbolo di addizione (⊕), va in add
        elif isinstance(statement_node, ast.Return) and ("⊕" in payload_value or "x + a" in payload_value or "self.x + a" in payload_value or "self.result" in payload_value):
            if len(body_stack) >= 2:
                class_body = body_stack[1]  # Body della classe
                for node in class_body:
                    if isinstance(node, ast.FunctionDef) and (node.name == "add" or node.name == "multiply"):
                        return node.body

        # Se è un return con simbolo di moltiplicazione (⊗), va in mul
        elif isinstance(statement_node, ast.Return) and ("⊗" in payload_value or "x * b" in payload_value or "self.x * b" in payload_value):
            if len(body_stack) >= 2:
                class_body = body_stack[1]  # Body della classe
                for node in class_body:
                    if isinstance(node, ast.FunctionDef) and node.name == "mul":
                        return node.body

        # Fallback: nessuna funzione appropriata trovata
        return None

    def _determine_block_placement(self, node: ast.AST, body_stack: List, current_index: int, symbols: List[str]) -> int:
        """
        Determina intelligentemente a che depth posizionare un blocco.

        Args:
            node: Nodo AST da posizionare
            body_stack: Stack corrente dei body
            current_index: Indice corrente nel flusso di simboli
            symbols: Lista completa dei simboli

        Returns:
            Target depth per il body_stack
        """
        # 🛠 LOGICA INTELLIGENTE PER POSIZIONAMENTO BLOCCHI

        # 🎯 PATCH 3: Placement raffinato per list-comp fuori dall'if
        # Se siamo in un blocco di controllo (If/For) e arriva un Assign/ListComp
        # con variabile non dichiarata in quel blocco -> bubbla di un livello
        if len(body_stack) > 2:  # module -> function -> control_block
            parent_body = body_stack[-2] if len(body_stack) >= 2 else None
            if parent_body and len(parent_body) > 0:
                parent_node = parent_body[-1]
                if isinstance(parent_node, ast.If) and isinstance(node, (ast.Assign, ast.ListComp, ast.Expr)):
                    print(f"   🎯 PATCH 3: Bubbling {type(node).__name__} out of If block")
                    return len(body_stack) - 1  # Sposta al livello funzione

        # FunctionDef/AsyncFunctionDef: controlla se c'è una classe aperta
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            # Cerca se c'è una classe aperta nel module_body
            if body_stack[0] and isinstance(body_stack[0][-1], ast.ClassDef):
                # C'è una classe aperta, la funzione va dentro la classe (depth 2)
                return 2
            # Altrimenti va al livello module
            return 1

        # For/While: guarda il contesto
        elif isinstance(node, (ast.For, ast.While)):
            # Se siamo al livello module, resta al module
            if len(body_stack) == 1:
                return 1
            # Se c'è una funzione aperta, va dentro la funzione
            elif len(body_stack) >= 2:
                return len(body_stack)
            # Altrimenti, chiudi al livello precedente
            else:
                return len(body_stack) - 1

        # If: va nel blocco corrente
        elif isinstance(node, ast.If):
            return len(body_stack)

        # ClassDef: sempre al livello module
        elif isinstance(node, ast.ClassDef):
            return 1

        # Default: livello corrente
        return len(body_stack)

    def _reorder_symbols_by_hierarchy(self, symbols: List[str], symbol_metadata: Dict) -> List[str]:
        """
        Riordina simboli per rispettare la gerarchia AST corretta.

        Args:
            symbols: Lista simboli originale
            symbol_metadata: Metadati dei simboli

        Returns:
            Lista simboli riordinata
        """
        # 🎯 PATCH 5: Crea mapping indice originale per stable-sort
        self._symbol_orig_index = {symbol: i for i, symbol in enumerate(symbols)}
        # 🎯 PATCH 5: PRIORITY TABLE AGGIORNATA PER ORDINE CORRETTO
        symbol_priority = {
            "⟡": 1, "⟨⟩": 1, "⊶": 1,  # function/class/async function definition
            "⟲": 2, "⟳": 2, "⟐": 2,   # for/while loops, try/except
            "◊": 3, "☇": 3,            # if/else
            "⟦⟧": 3,                   # 🎯 PATCH 5: variable_assignment priorità 3 (pivot prima)
            "⟔◊": 4,                   # conditional_assignment priorità 4 (dopo pivot)
            "⤴": 5, "⟲⟵": 5,          # return, recursive call
            "⊗": 6, "⊕": 6, "⟔": 6, "⊞": 4, "⊷": 6  # expr (mul, add, listcomp, await), augassign
        }

        # 🎯 PATCH 5: PRIORITY TABLE PATTERN NAMES AGGIORNATA
        # 3️⃣ PRIORITY TABLE STABILE - NIENTE 999 FALLBACK
        self.priority = {
            # 1 → struttura
            "function_definition": 1, "class_definition": 1, "async_function": 1,
            # 2 → loop
            "for_loop": 2, "while_loop": 2,
            # 3 → controllo e assignment base
            "if_statement": 3, "else_clause": 3, "variable_assignment": 3,  # 🎯 PATCH 5: variable_assignment priorità 3
            # 4 → assignment condizionali
            "aug_assign": 4, "conditional_assignment": 4,
            # 5 → uscita
            "return_statement": 5, "recursive_function_call": 5,
            # 6 → espressioni
            "binary_add": 6, "binary_sub": 6, "binary_mul": 6, "binary_div": 6, "list_comprehension": 6, "await_expression": 6
        }

        # Crea lista di (simbolo, priorità, indice_originale)
        symbol_priorities = []
        for i, symbol in enumerate(symbols):
            # 4️⃣ NUMERI E TOKEN RESIDUALI: IGNORA INVECE DI 999
            if re.fullmatch(r"\d+", symbol):      # solo cifre
                print(f"   {symbol} - numero ignorato")
                continue                          # saltalo

            metadata = symbol_metadata.get(symbol, {})
            pattern_name = metadata.get("pattern_name", "unknown")

            # 3️⃣ USA SYMBOL_PRIORITY PRIMA DI SELF.PRIORITY
            base_symbol = symbol.split("§")[0] if "§" in symbol else symbol
            priority = symbol_priority.get(base_symbol, self.priority.get(pattern_name, 50))   # 50 = appendi in fondo

            symbol_priorities.append((symbol, priority, i))

        # 🎯 PATCH 5: Stable-sort per simboli con stessa priorità
        symbol_priorities.sort(key=lambda x: (
            x[1],  # 1) priorità
            self._symbol_orig_index.get(x[0], x[2])  # 2) indice originale per stable-sort
        ))

        # Estrai simboli riordinati
        reordered = [item[0] for item in symbol_priorities]

        print(f"   Riordino gerarchia:")
        for symbol, priority, orig_idx in symbol_priorities:
            pattern = symbol_metadata.get(symbol, {}).get("pattern_name", "unknown")
            print(f"     {symbol} ({pattern}) - priorità {priority}, orig {orig_idx}")

        return reordered

    # 4️⃣ MAPPING VELOCE PER NOMI E COSTANTI
    def _name_from_payload(self, value: str, ctx: ast.expr_context = None) -> ast.Name:
        """Crea nodo Name da payload."""
        if ctx is None:
            ctx = ast.Load()
        return ast.Name(id=value, ctx=ctx)

    def _const(self, val: Any) -> ast.Constant:
        """Crea nodo Constant da valore."""
        return ast.Constant(value=val)

    def _parse_value_from_payload(self, payload_value: str) -> ast.expr:
        """
        Parsa valore da payload e crea nodo AST appropriato.

        Args:
            payload_value: Valore dal payload

        Returns:
            Nodo AST (Name, Constant, etc.)
        """
        if not payload_value:
            return self._const(None)

        # Stringhe
        if payload_value.startswith('"') and payload_value.endswith('"'):
            return self._const(payload_value[1:-1])
        elif payload_value.startswith("'") and payload_value.endswith("'"):
            return self._const(payload_value[1:-1])

        # Numeri
        elif payload_value.replace('.', '').replace('-', '').isdigit():
            try:
                value = int(payload_value) if '.' not in payload_value else float(payload_value)
                return self._const(value)
            except ValueError:
                return self._name_from_payload(payload_value)

        # Booleani
        elif payload_value in ["True", "False"]:
            return self._const(payload_value == "True")

        # None
        elif payload_value == "None":
            return self._const(None)

        # Identificatori
        elif payload_value.replace('_', '').isalnum() and not payload_value[0].isdigit():
            return self._name_from_payload(payload_value)

        # Fallback: tratta come stringa
        return self._const(payload_value)

    def _reverse_symbol_mapping(self,
                               compressed_symbols: List[str],
                               metadata: Dict[str, Any],
                               target_language: str = "python") -> MappingResult:
        """
        Implementa mappatura inversa simboli → AST.
        
        Args:
            compressed_symbols: Lista di simboli compressi
            metadata: Metadati di compressione
            target_language: Linguaggio target
            
        Returns:
            MappingResult con struttura AST ricostruita
        """
        print(f"🔄 Mappatura inversa simboli → AST")
        print(f"   - Simboli da mappare: {len(compressed_symbols)}")
        
        # Reset counter per placeholder consistenti
        self.placeholder_counter = 0
        
        # Lista nodi AST
        ast_nodes = []
        placeholders_used = []
        mapping_info = {
            "total_symbols": len(compressed_symbols),
            "mapped_symbols": 0,
            "unknown_symbols": 0,
            "symbol_mappings": {}
        }
        
        # 2️⃣ PARSER ESPRESSIONI STACK-BASED + SCOPE-STACK
        payload = metadata.get("payload", [])
        symbol_metadata = metadata.get("symbol_metadata", {})

        # 🛠 RIORDINO SIMBOLI PER GERARCHIA CORRETTA
        reordered_symbols = self._reorder_symbols_by_hierarchy(compressed_symbols, symbol_metadata)
        print(f"   Simboli riordinati: {reordered_symbols}")

        # Stack per parsing espressioni
        expr_stack = []
        op_stack = []

        # STEP FINALE S-1: SCOPE-STACK PER GERARCHIA BLOCCHI
        module_body = []
        body_stack = [module_body]  # Stack di body correnti
        current_nodes = []  # Nodi temporanei prima di assegnare a un body

        # 1️⃣ PATCH POST-POP: Flag per distribuzione statement intelligente
        last_was_funcdef = False
        pending_simple_stmt = False

        for i, symbol in enumerate(reordered_symbols):
            print(f"   Processando simbolo {i+1}/{len(compressed_symbols)}: {symbol}")

            # 4️⃣ IGNORA TOKEN NUMERICI RESIDUALI
            if symbol.isdecimal():
                continue           # salta numeri "nudi" nel flusso

            # 2️⃣ PATCH: IGNORA IDENTIFICATORI SEMPLICI UNKNOWN
            # Pattern per identificatori semplici (parole senza simboli speciali)
            _UNKNOWN_RX = re.compile(r"^\w+$")
            if _UNKNOWN_RX.match(symbol) and symbol not in self.symbol_to_ast_map:
                # È un identificatore nudo che non sappiamo dove mettere → salta
                print(f"   Ignorato identificatore unknown: {symbol}")
                continue

            # 1️⃣ ELSE-CLAUSE: POSIZIONAMENTO SICURO
            if symbol == "☇":          # else_clause
                # cerca l'If più vicino nello stack
                parent_if = next((n for n in reversed(body_stack[-1]) if isinstance(n, ast.If)), None)
                if parent_if is None:
                    continue                  # else "orfano": lo ignoro

                parent_if.orelse = []
                # sostituisco **l'ultimo** body sullo stack con quello dell'else
                if body_stack:
                    body_stack[-1] = parent_if.orelse
                continue                          # passa al symbol successivo

            # STEP FINALE S-1: GESTIONE TOKEN END
            elif symbol == "⟩":
                # Token di chiusura blocco
                if len(body_stack) > 1:
                    # Chiudi il blocco corrente
                    closed_body = body_stack.pop()
                    # 2️⃣ PULIZIA PASS E DUPLICATI
                    if closed_body:
                        # Rimuovi Pass placeholder
                        closed_body[:] = [n for n in closed_body if not isinstance(n, ast.Pass)]
                        # Rimuovi Return duplicati (mantieni solo l'ultimo)
                        returns = [i for i, n in enumerate(closed_body) if isinstance(n, ast.Return)]
                        if len(returns) > 1:
                            # Rimuovi tutti tranne l'ultimo
                            for i in reversed(returns[:-1]):
                                closed_body.pop(i)
                            print(f"   Rimossi {len(returns)-1} Return duplicati")
                print(f"   Chiuso blocco, stack depth: {len(body_stack)}")
                continue

            # FASE 2 - GESTISCI SIMBOLI CON PAYLOAD
            if "§" in symbol:
                # Simbolo con payload: ⟨⟩§0§
                print(f"   DEBUG: Simbolo con payload: {symbol}")
                base_symbol, payload_idx_str, _ = symbol.split("§")
                payload_idx = int(payload_idx_str)
                payload_value = payload[payload_idx] if payload_idx < len(payload) else None

                # 2️⃣ DECODIFICA RICORSIVA PAYLOAD SICURA: Sostituisci simboli grezzi
                if payload_value:
                    payload_value = self._strip_all_symbols(payload_value, symbol_metadata)

                print(f"   DEBUG: Base={base_symbol}, Payload={payload_value}")

                node = self._create_ast_node_with_payload(
                    base_symbol,
                    payload_value,
                    symbol_metadata.get(symbol, {}),
                    metadata
                )

                # 2️⃣ GESTIONE ESPRESSIONI: aggiungi a stack se è espressione
                if isinstance(node, (ast.BinOp, ast.ListComp, ast.Name, ast.Constant)):
                    expr_stack.append(node)

                    # 🎯 PATCH 2: List comprehensions come assignment intelligente
                    if isinstance(node, ast.ListComp):
                        # Cerca se questa list comprehension dovrebbe essere un assignment
                        # Basandoci sul payload, potrebbe essere left/middle/right = [...]
                        payload_hint = payload_value if payload_value else ""

                        # Se il payload contiene pattern di variabili (left, middle, right)
                        if any(var in payload_hint for var in ["left", "middle", "right", "filtered", "result"]):
                            # Crea assignment invece di espressione
                            var_name = "left" if "< pivot" in payload_hint else \
                                      "middle" if "== pivot" in payload_hint else \
                                      "right" if "> pivot" in payload_hint else \
                                      "filtered" if "filtered" in payload_hint else "result"

                            assign_node = ast.Assign(
                                targets=[ast.Name(id=var_name, ctx=ast.Store())],
                                value=node
                            )

                            # Usa distribuzione intelligente per assignment
                            target_body = self._find_appropriate_function_body(assign_node, body_stack, {"payload_value": f"{var_name}={payload_hint}"})
                            if target_body is not None:
                                target_body.append(assign_node)
                                print(f"   🎯 List comprehension '{var_name}' assegnata a funzione")
                            else:
                                body_stack[-1].append(assign_node)
                                print(f"   🎯 List comprehension '{var_name}' aggiunta al body corrente")
                        else:
                            # Espressione normale
                            body_stack[-1].append(ast.Expr(value=node))
                            print(f"   Aggiunta list comprehension come espressione al body corrente")
                    else:
                        # STEP FINALE S-1: ALTRE ESPRESSIONI VANNO AL BODY CORRENTE
                        body_stack[-1].append(ast.Expr(value=node))
                        print(f"   Aggiunta espressione al body corrente, stack depth: {len(body_stack)}")
                else:
                    # 2️⃣ RIDUCI STACK QUANDO INCONTRI STATEMENT
                    if expr_stack:
                        # Prendi l'ultima espressione dallo stack
                        last_expr = expr_stack.pop()
                        # Integra espressione nel statement
                        node = self._integrate_expression_in_statement(node, last_expr)
                        # Pulisci stack rimanente
                        expr_stack.clear()
                        op_stack.clear()

                    # STEP FINALE S-1: GESTIONE APERTURA BLOCCHI CON INFERENZA INTELLIGENTE
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.For, ast.While, ast.If, ast.ClassDef, ast.Try)):
                        # 1️⃣ PATCH POST-POP: CHIUSURA INTELLIGENTE FUNZIONI
                        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                            # Se c'era una FunctionDef precedente E abbiamo processato statement, chiudila
                            if last_was_funcdef and pending_simple_stmt:
                                print(f"   Post-pop: chiusura FunctionDef precedente dopo statement")
                                while len(body_stack) > 2:  # Torna al livello classe
                                    closed_body = body_stack.pop()
                                    if closed_body:
                                        closed_body[:] = [n for n in closed_body if not isinstance(n, ast.Pass)]

                            # Aggiorna flag
                            last_was_funcdef = True
                            pending_simple_stmt = True
                        else:
                            # Altri blocchi (If, For, While, ClassDef)
                            last_was_funcdef = False
                            pending_simple_stmt = False

                        # 🛠 INFERENZA INTELLIGENTE: Determina dove posizionare il blocco
                        target_depth = self._determine_block_placement(node, body_stack, i, compressed_symbols)

                        # Chiudi blocchi fino al target depth
                        while len(body_stack) > target_depth:
                            closed_body = body_stack.pop()
                            if closed_body:
                                closed_body[:] = [n for n in closed_body if not isinstance(n, ast.Pass)]

                        print(f"   Posizionato {type(node).__name__} a depth {len(body_stack)}")

                        # Aggiungi al body corrente
                        body_stack[-1].append(node)

                        # 🔧 TRY/EXCEPT SUPPORT - Gestione stack multipli per Try
                        if isinstance(node, ast.Try):
                            # Push solo il body principale del try
                            body_stack.append(node.body)
                            print(f"   Aperto blocco Try (body principale), stack depth: {len(body_stack)}")
                        else:
                            # Push nuovo body sullo stack per altri blocchi
                            body_stack.append(node.body)
                            print(f"   Aperto blocco {type(node).__name__}, stack depth: {len(body_stack)}")
                    else:
                        # 🎯 PATCH 3: DISTRIBUZIONE INTELLIGENTE STATEMENT CON ANTI-DUPLICAZIONE
                        if isinstance(node, (ast.Assign, ast.Return, ast.AugAssign)):
                            print(f"   Simple statement rilevato: {type(node).__name__}")

                            # se abbiamo appena chiuso un FunctionDef e arriva il 1° simple-stmt
                            if pending_simple_stmt and body_stack and len(body_stack) > 1:
                                # Trova l'ultimo FunctionDef nello stack
                                for i in range(len(body_stack) - 1, 0, -1):
                                    if (i < len(body_stack) and
                                        body_stack[i] and
                                        len(body_stack[i]) > 0 and
                                        isinstance(body_stack[i][-1], ast.FunctionDef)):
                                        body_stack.pop()
                                        pending_simple_stmt = False
                                        break

                            # 🎯 PATCH 4: Anti-duplicazione Return con posizionamento intelligente
                            if isinstance(node, ast.Return):
                                is_simple_return = (isinstance(node.value, ast.Name) and node.value.id in ["arr", "None"]) or \
                                                  (isinstance(node.value, ast.Constant) and node.value.value is None)

                                # Se è un return semplice, prova a metterlo nell'if corrente
                                if is_simple_return and len(body_stack) >= 3:
                                    # Siamo in un blocco di controllo (if), metti il return lì
                                    if_body = body_stack[-1]  # Body dell'if corrente
                                    if_body.append(node)
                                    print(f"   🎯 PATCH 4: Return semplice aggiunto al blocco if")
                                else:
                                    # Return complesso o nessun if disponibile
                                    target_body = self._find_appropriate_function_body(node, body_stack, symbol_metadata.get(symbol, {}))
                                    if target_body is not None:
                                        # Controlla se esiste già un return complesso nella funzione
                                        existing_returns = [stmt for stmt in target_body if isinstance(stmt, ast.Return)]

                                        has_complex_return = any(
                                            not ((isinstance(ret.value, ast.Name) and ret.value.id in ["arr", "None"]) or
                                                 (isinstance(ret.value, ast.Constant) and ret.value.value is None))
                                            for ret in existing_returns
                                        )

                                        if is_simple_return and has_complex_return:
                                            print(f"   🎯 PATCH 4: Skipping return semplice, esiste già return complesso")
                                        else:
                                            target_body.append(node)
                                            print(f"   🎯 PATCH 4: Return aggiunto a funzione specifica")
                                    else:
                                        body_stack[-1].append(node)
                                        print(f"   Return aggiunto al body corrente")
                            else:
                                # Altri statement (Assign, AugAssign)
                                target_body = self._find_appropriate_function_body(node, body_stack, symbol_metadata.get(symbol, {}))
                                if target_body is not None:
                                    target_body.append(node)
                                    print(f"   Statement assegnato a funzione specifica")
                                else:
                                    # Fallback: aggiungi al body corrente
                                    body_stack[-1].append(node)
                                    print(f"   Statement aggiunto al body corrente (fallback)")
                        else:
                            # PATCH IMMEDIATA: FUSIONA LISTCOMP + ASSIGN (MULTIPLE + CROSS-SCOPE)
                            if isinstance(node, ast.Assign):
                                # Cerca ListComp in tutti i body dello stack (dal più profondo al più superficiale)
                                listcomp_found = False
                                for stack_level in range(len(body_stack) - 1, -1, -1):
                                    current_body = body_stack[stack_level]
                                    # Cerca la PRIMA Expr(ListComp) disponibile nel body (FIFO)
                                    listcomp_index = -1
                                    for idx in range(len(current_body)):
                                        if (isinstance(current_body[idx], ast.Expr) and
                                            isinstance(current_body[idx].value, ast.ListComp)):
                                            listcomp_index = idx
                                            break

                                    if listcomp_index >= 0:
                                        # Sostituisci il valore dell'assign con la ListComp
                                        listcomp = current_body[listcomp_index].value
                                        node.value = listcomp
                                        # Rimuovi l'Expr orfana
                                        current_body.pop(listcomp_index)
                                        print(f"   Fusa ListComp (stack {stack_level}, index {listcomp_index}) con Assignment")
                                        listcomp_found = True
                                        break

                                if not listcomp_found:
                                    print(f"   Assignment senza ListComp disponibile")

                            # Statement normale - aggiungi al body corrente
                            body_stack[-1].append(node)

                mapping_info["mapped_symbols"] += 1

                # Salva mapping info
                mapping_info["symbol_mappings"][symbol] = {
                    "ast_type": self.symbol_to_ast_map.get(base_symbol, "Unknown"),
                    "position": i,
                    "payload_value": payload_value,
                    "placeholder_used": f"payload_{payload_idx}"
                }

            # S-1: GESTISCI SIMBOLI SENZA PAYLOAD (operatori, etc.)
            elif symbol in self.symbol_to_ast_map:
                # Simbolo normale mappato
                node = self._create_ast_node_from_symbol_metadata(symbol, symbol_metadata.get(symbol, {}))

                # 🔧 ASYNC/TRY/EXCEPT SUPPORT - Gestione blocchi senza payload
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.For, ast.While, ast.If, ast.ClassDef, ast.Try)):
                    # Blocco senza payload - gestisci come blocco normale
                    target_depth = self._determine_block_placement(node, body_stack, i, compressed_symbols)

                    # Chiudi blocchi fino al target depth
                    while len(body_stack) > target_depth:
                        closed_body = body_stack.pop()
                        if closed_body:
                            closed_body[:] = [n for n in closed_body if not isinstance(n, ast.Pass)]

                    print(f"   Posizionato {type(node).__name__} (senza payload) a depth {len(body_stack)}")

                    # Aggiungi al body corrente
                    body_stack[-1].append(node)
                    # Push nuovo body sullo stack
                    body_stack.append(node.body)
                    print(f"   Aperto blocco {type(node).__name__}, stack depth: {len(body_stack)}")

                # 2️⃣ GESTIONE ESPRESSIONI
                elif isinstance(node, (ast.BinOp, ast.Name, ast.Constant)):
                    expr_stack.append(node)
                else:
                    ast_nodes.append(node)

                mapping_info["mapped_symbols"] += 1

                # Salva mapping info
                mapping_info["symbol_mappings"][symbol] = {
                    "ast_type": self.symbol_to_ast_map[symbol],
                    "position": i,
                    "placeholder_used": f"symbol_{symbol}"
                }

            # Gestisci simboli ripetuti (es. ⟨⟩³)
            elif '³' in symbol:
                base_symbol = symbol.replace('³', '')
                for _ in range(3):
                    node = self._create_ast_node(base_symbol, metadata)
                    ast_nodes.append(node)
                    mapping_info["mapped_symbols"] += 1

            # Gestisci pattern complessi
            elif symbol in ["⟈", "⟳◊⟵", "⊶⟲"]:
                complex_nodes = self._handle_complex_pattern(symbol, metadata)
                ast_nodes.extend(complex_nodes)
                mapping_info["mapped_symbols"] += len(complex_nodes)

            # Simbolo normale
            elif symbol in self.symbol_to_ast_map:
                node = self._create_ast_node(symbol, metadata)
                ast_nodes.append(node)
                mapping_info["mapped_symbols"] += 1

                # Salva mapping info
                mapping_info["symbol_mappings"][symbol] = {
                    "ast_type": self.symbol_to_ast_map[symbol],
                    "position": i,
                    "placeholder_used": f"placeholder_{self.placeholder_counter}"
                }

            # Simbolo sconosciuto o testo normale
            else:
                # PATCH IMMEDIATA: Controlla se è un binary operator con payload
                if symbol.startswith('⊕') and '§' in symbol:
                    # È un binary_add con payload, processalo come tale
                    print(f"   DEBUG: Binary add rilevato in simboli sconosciuti: {symbol}")
                    parts = symbol.split('§')
                    if len(parts) >= 2:
                        base_symbol = parts[0]  # ⊕
                        payload_idx_str = parts[1].rstrip('§')  # 3
                        try:
                            payload_idx = int(payload_idx_str)
                            payload_value = payload[payload_idx] if payload_idx < len(payload) else None
                            print(f"   DEBUG: Payload estratto - Base={base_symbol}, Payload={payload_value}")

                            # Crea nodo con template binary_add
                            node = self._create_ast_node_with_payload(
                                base_symbol,
                                payload_value,
                                symbol_metadata.get(symbol, {}),
                                metadata
                            )

                            # Aggiungi al body corrente
                            body_stack[-1].append(node)
                            print(f"   Binary add decodificato e aggiunto al body")
                            mapping_info["mapped_symbols"] += 1
                            continue
                        except (ValueError, IndexError) as e:
                            print(f"   DEBUG: Errore parsing payload per {symbol}: {e}")

                # FASE 1 - GESTIONE INTELLIGENTE SIMBOLI SCONOSCIUTI
                node = self._handle_unknown_symbol(symbol, i, compressed_symbols)
                if node:
                    # STEP FINALE S-1: AGGIUNGI AL BODY CORRENTE
                    body_stack[-1].append(node)
                    mapping_info["unknown_symbols"] += 1

        # 2️⃣ CHIUSURA AUTOMATICA BLOCCHI RIMANENTI
        while len(body_stack) > 1:
            closed_body = body_stack.pop()
            if closed_body:
                # Rimuovi Pass placeholder
                closed_body[:] = [n for n in closed_body if not isinstance(n, ast.Pass)]
                # Rimuovi Return duplicati (mantieni solo l'ultimo)
                returns = [i for i, n in enumerate(closed_body) if isinstance(n, ast.Return)]
                if len(returns) > 1:
                    for i in reversed(returns[:-1]):
                        closed_body.pop(i)
            print(f"   Auto-chiuso blocco, stack depth: {len(body_stack)}")

        # 2️⃣ PULIZIA FINALE: Rimuovi solo Pass auto-inseriti
        def clean_body_recursive(node):
            # 🔧 FIX RICORSIONE: Usa ast.iter_child_nodes per evitare loop infiniti
            if hasattr(node, 'body') and isinstance(node.body, list):
                # Rimuovi solo Pass auto-inseriti (con _ng_auto=True)
                node.body[:] = [n for n in node.body if not (isinstance(n, ast.Pass) and getattr(n, '_ng_auto', False))]
            if hasattr(node, 'orelse') and isinstance(node.orelse, list):
                node.orelse[:] = [n for n in node.orelse if not (isinstance(n, ast.Pass) and getattr(n, '_ng_auto', False))]
            # 🔧 TRY/EXCEPT SUPPORT - Pulizia handlers
            if hasattr(node, 'handlers') and isinstance(node.handlers, list):
                for handler in node.handlers:
                    if hasattr(handler, 'body') and isinstance(handler.body, list):
                        handler.body[:] = [n for n in handler.body if not (isinstance(n, ast.Pass) and getattr(n, '_ng_auto', False))]
            if hasattr(node, 'finalbody') and isinstance(node.finalbody, list):
                node.finalbody[:] = [n for n in node.finalbody if not (isinstance(n, ast.Pass) and getattr(n, '_ng_auto', False))]

            # Pulisci ricorsivamente solo i nodi AST figli (non le liste)
            for child in ast.iter_child_nodes(node):
                clean_body_recursive(child)

        # Applica pulizia ricorsiva
        for node in module_body:
            clean_body_recursive(node)

        # 🎯 PATCH 3: Dedup & Clean - Rimozione Return Placeholder
        print(f"   🎯 PATCH 3: Inizio deduplication, module_body ha {len(module_body)} elementi")

        for func in [n for n in ast.walk(ast.Module(body=module_body, type_ignores=[])) if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef))]:
            print(f"   🎯 PATCH 3: Analizzando funzione {func.name} con {len(func.body)} statements")

            # Rimuovi return placeholder (return arr) se esiste un return più complesso
            returns = [stmt for stmt in func.body if isinstance(stmt, ast.Return)]
            print(f"   🎯 PATCH 3: Trovati {len(returns)} return statements in {func.name}")

            if len(returns) > 1:
                # Cerca return placeholder (return arr/None/semplici)
                placeholder_returns = []
                complex_returns = []

                for i, ret in enumerate(returns):
                    ret_code = ast.unparse(ret)
                    print(f"   🎯 PATCH 3: Return {i+1}: {ret_code}")

                    if isinstance(ret.value, ast.Name) and ret.value.id in ["arr", "None"]:
                        placeholder_returns.append(ret)
                        print(f"   🎯 PATCH 3: → Classificato come PLACEHOLDER")
                    elif isinstance(ret.value, ast.Constant) and ret.value.value is None:
                        placeholder_returns.append(ret)
                        print(f"   🎯 PATCH 3: → Classificato come PLACEHOLDER")
                    else:
                        complex_returns.append(ret)
                        print(f"   🎯 PATCH 3: → Classificato come COMPLEX")

                # Se abbiamo return complessi, rimuovi i placeholder
                if complex_returns and placeholder_returns:
                    print(f"   🎯 PATCH 3: Rimuovendo {len(placeholder_returns)} placeholder, mantenendo {len(complex_returns)} complessi")
                    for placeholder in placeholder_returns:
                        if placeholder in func.body:
                            func.body.remove(placeholder)
                            print(f"   🎯 PATCH 3: ✅ Rimosso return placeholder: {ast.unparse(placeholder)}")
                else:
                    print(f"   🎯 PATCH 3: Nessuna rimozione necessaria (complex={len(complex_returns)}, placeholder={len(placeholder_returns)})")

            # 3️⃣ PATCH: Funzioni vuote ⇒ pass
            if not func.body:
                func.body.append(ast.Pass())

        # 🔧 TRY/EXCEPT SUPPORT - PATCH 3: Blocchi Try vuoti ⇒ pass
        for try_node in [n for n in ast.walk(ast.Module(body=module_body, type_ignores=[])) if isinstance(n, ast.Try)]:
            if not try_node.body:
                try_node.body.append(ast.Pass())
            for handler in try_node.handlers:
                if not handler.body:
                    handler.body.append(ast.Pass())
            if hasattr(try_node, 'finalbody') and not try_node.finalbody:
                # finalbody può rimanere vuoto
                pass

        # STEP FINALE S-1: USA MODULE_BODY CON GERARCHIA CORRETTA
        ast_structure = ast.Module(body=module_body, type_ignores=[])
        
        # Metadati di ricostruzione
        reconstruction_metadata = {
            "target_language": target_language,
            "total_nodes_created": len(module_body),
            "placeholder_counter": self.placeholder_counter,
            "compression_metadata": metadata
        }
        
        result = MappingResult(
            ast_structure=ast_structure,
            mapping_info=mapping_info,
            placeholders_used=placeholders_used,
            reconstruction_metadata=reconstruction_metadata
        )
        
        print(f"✅ Mappatura completata:")
        print(f"   - Nodi AST creati: {len(module_body)}")
        print(f"   - Simboli mappati: {mapping_info['mapped_symbols']}")
        print(f"   - Simboli sconosciuti: {mapping_info['unknown_symbols']}")
        print(f"   - Stack depth finale: {len(body_stack)}")
        
        return result
    
    def _handle_complex_pattern(self, symbol: str, metadata: Dict) -> List[ast.AST]:
        """Gestisce pattern complessi multi-nodo."""
        nodes = []
        
        if symbol == "⟈":  # If-elif-else chain
            # Crea catena if-elif-else
            if_node = self._create_ast_node("◊", metadata)
            elif_node = self._create_ast_node("◊", metadata)
            else_node = ast.If(
                test=ast.Constant(value=True),  # else equivale a if True
                body=[ast.Pass()],
                orelse=[]
            )
            
            # Collega nodi
            if_node.orelse = [elif_node]
            elif_node.orelse = [else_node]
            
            nodes.append(if_node)
        
        elif symbol == "⟳◊⟵":  # For-if-return pattern
            # Crea for loop con if e return
            for_node = self._create_ast_node("⟲", metadata)
            if_node = self._create_ast_node("◊", metadata)
            return_node = ast.Return(value=ast.Constant(value=None))
            
            # Struttura: for -> if -> return
            if_node.body = [return_node]
            for_node.body = [if_node]
            
            nodes.append(for_node)
        
        elif symbol == "⊶⟲":  # Async loop pattern
            # Crea async function con loop
            async_func = self._create_ast_node("⊶", metadata)
            for_loop = self._create_ast_node("⟲", metadata)
            
            # Async function contiene loop
            async_func.body = [for_loop]
            
            nodes.append(async_func)
        
        return nodes

    def _handle_unknown_symbol(self, symbol: str, position: int, all_symbols: List[str]) -> Optional[ast.AST]:
        """
        Gestisce simboli sconosciuti con logica intelligente.

        Args:
            symbol: Simbolo da gestire
            position: Posizione nell'array di simboli
            all_symbols: Tutti i simboli per contesto

        Returns:
            Nodo AST appropriato o None
        """
        if not symbol.strip():
            return None

        # FASE 1 - RICONOSCIMENTO PATTERN SPECIFICI

        # 1. Keyword Python specifiche
        if symbol == "return":
            # Se seguito da un valore, crea Return con valore
            if position + 1 < len(all_symbols):
                next_symbol = all_symbols[position + 1]
                if next_symbol.startswith('"') or next_symbol.startswith("'"):
                    # Return con stringa
                    value = ast.Constant(value=next_symbol.strip('"\''))
                    return ast.Return(value=value)
                elif next_symbol.replace('.', '').isdigit():
                    # Return con numero
                    try:
                        value = int(next_symbol) if '.' not in next_symbol else float(next_symbol)
                        return ast.Return(value=ast.Constant(value=value))
                    except ValueError:
                        return ast.Return(value=ast.Constant(value=None))
                else:
                    # Return con identificatore
                    return ast.Return(value=ast.Name(id=next_symbol, ctx=ast.Load()))
            else:
                # Return semplice
                return ast.Return(value=ast.Constant(value=None))

        # 2. Stringhe letterali
        if symbol.startswith('"') and symbol.endswith('"'):
            return ast.Expr(value=ast.Constant(value=symbol[1:-1]))
        elif symbol.startswith("'") and symbol.endswith("'"):
            return ast.Expr(value=ast.Constant(value=symbol[1:-1]))

        # 3. Numeri
        elif symbol.replace('.', '').replace('-', '').isdigit():
            try:
                value = int(symbol) if '.' not in symbol else float(symbol)
                return ast.Expr(value=ast.Constant(value=value))
            except ValueError:
                return ast.Expr(value=ast.Constant(value=symbol))

        # 4. Identificatori validi
        elif symbol.replace('_', '').isalnum() and not symbol[0].isdigit():
            return ast.Expr(value=ast.Name(id=symbol, ctx=ast.Load()))

        # 5. Operatori comuni
        elif symbol in ['+', '-', '*', '/', '==', '!=', '<', '>', '<=', '>=']:
            # Crea placeholder per operatori - verranno gestiti in fase di ricostruzione
            return ast.Expr(value=ast.Constant(value=f"OPERATOR_{symbol}"))

        # 6. Parentesi e delimitatori
        elif symbol in ['(', ')', '[', ']', '{', '}', ':', ';', ',']:
            # Ignora delimitatori - verranno gestiti dalla struttura AST
            return None

        # 7. Altro testo - crea costante generica
        else:
            return ast.Expr(value=ast.Constant(value=symbol))

    def _reconstruct_intelligent_ast(self,
                                   ast_nodes: List[ast.AST],
                                   symbols: List[str],
                                   metadata: Dict) -> ast.Module:
        """Ricostruisce AST in modo intelligente preservando struttura."""

        # Se abbiamo pochi nodi, prova a ricostruire struttura logica
        if len(ast_nodes) <= 10:
            structured_body = self._build_structured_body(ast_nodes, symbols)
        else:
            # Per molti nodi, usa struttura semplice
            structured_body = ast_nodes if ast_nodes else [ast.Pass()]

        return ast.Module(body=structured_body, type_ignores=[])

    def _build_structured_body(self, nodes: List[ast.AST], symbols: List[str]) -> List[ast.AST]:
        """Costruisce corpo strutturato da nodi AST."""
        structured = []
        i = 0

        while i < len(nodes):
            node = nodes[i]

            # FASE 1 - GESTIONE INTELLIGENTE FUNZIONI
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Cerca nodi successivi che potrebbero essere il corpo
                body_nodes = []
                j = i + 1

                # Raccogli nodi fino alla prossima funzione/classe
                while j < len(nodes) and not isinstance(nodes[j], (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                    current_node = nodes[j]

                    # FASE 1 - FILTRA NODI APPROPRIATI PER IL CORPO
                    if isinstance(current_node, ast.Return):
                        # Return statements vanno sempre nel corpo
                        body_nodes.append(current_node)
                    elif isinstance(current_node, (ast.If, ast.For, ast.While, ast.Try, ast.With)):
                        # Control flow statements
                        body_nodes.append(current_node)
                    elif isinstance(current_node, ast.Assign):
                        # Assignments
                        body_nodes.append(current_node)
                    elif isinstance(current_node, ast.Expr):
                        # Expressions - solo se non sono placeholder
                        if not (isinstance(current_node.value, ast.Constant) and
                               isinstance(current_node.value.value, str) and
                               current_node.value.value.startswith("OPERATOR_")):
                            body_nodes.append(current_node)
                    j += 1

                # Se abbiamo trovato contenuto, aggiungilo al corpo della funzione
                if body_nodes:
                    node.body = body_nodes
                    i = j  # Salta i nodi processati
                else:
                    # FASE 1 - CORPO VUOTO PIÙ INTELLIGENTE
                    # Se non abbiamo trovato nodi, ma c'è un return nella lista originale
                    if any("return" in str(s) for s in symbols):
                        node.body = [ast.Return(value=ast.Constant(value=None))]
                    else:
                        node.body = [ast.Pass()]
                    i += 1

                structured.append(node)

            # Se è una classe, gestisci similarmente
            elif isinstance(node, ast.ClassDef):
                body_nodes = []
                j = i + 1

                while j < len(nodes) and not isinstance(nodes[j], ast.ClassDef):
                    if isinstance(nodes[j], (ast.FunctionDef, ast.AsyncFunctionDef)):
                        body_nodes.append(nodes[j])
                    j += 1

                if body_nodes:
                    node.body = body_nodes
                    i = j
                else:
                    node.body = [ast.Pass()]
                    i += 1

                structured.append(node)

            # Altri nodi: aggiungi direttamente
            else:
                structured.append(node)
                i += 1

        return structured if structured else [ast.Pass()]

    def reverse_map(self,
                   compressed_symbols: List[str],
                   metadata: Dict[str, Any],
                   target_language: str = "python") -> MappingResult:
        """
        Interfaccia pubblica per mappatura inversa.
        
        Args:
            compressed_symbols: Simboli da mappare
            metadata: Metadati di compressione
            target_language: Linguaggio target
            
        Returns:
            MappingResult con struttura AST
        """
        return self._reverse_symbol_mapping(compressed_symbols, metadata, target_language)
