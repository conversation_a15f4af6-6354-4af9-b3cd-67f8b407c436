#!/usr/bin/env python3

from neuroglyph.core.encoder.encoder import NGEncoder

# Codice async function dal test
async_code = """
async def fetch_data(url):
    response = await http_client.get(url)
    return response.json()
""".strip()

print("🔍 ANALISI PROBLEMA ASYNC FUNCTION")
print("=" * 50)

encoder = NGEncoder()
result = encoder.round_trip_test(async_code, encoding_level=3)

print("\n📝 CODICE ORIGINALE:")
print(async_code)

print("\n🔧 SIMBOLI GENERATI:")
print(result.encoding_result.compressed_symbols)

print("\n🔄 CODICE RICOSTRUITO:")
print(result.reconstructed_code)

print("\n❌ ERRORE SINTASSI:")
try:
    import ast
    ast.parse(result.reconstructed_code)
    print("✅ Sintassi valida!")
except SyntaxError as e:
    print(f"❌ SyntaxError: {e}")
    print(f"   Linea {e.lineno}: {e.text}")

print("\n📊 METRICHE:")
print(f"   - Fidelity: {result.fidelity_score:.3f}")
print(f"   - Compressione: {result.encoding_result.compression_result.compression_ratio:.1f}%")
print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
