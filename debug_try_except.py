#!/usr/bin/env python3

from neuroglyph.core.encoder.encoder import NGEncoder

# Codice try/except semplice per test
try_except_code = """
try:
    x = 1
except:
    x = 0
""".strip()

print("🔍 ANALISI PROBLEMA TRY/EXCEPT")
print("=" * 50)

encoder = NGEncoder()
result = encoder.round_trip_test(try_except_code, encoding_level=3)

print("\n📝 CODICE ORIGINALE:")
print(try_except_code)

print("\n🔧 SIMBOLI GENERATI:")
print(result.encoding_result.compressed_symbols)

print("\n🔄 CODICE RICOSTRUITO:")
print(result.reconstructed_code)

print("\n❌ ERRORE SINTASSI:")
try:
    import ast
    ast.parse(result.reconstructed_code)
    print("✅ Sintassi valida!")
except SyntaxError as e:
    print(f"❌ SyntaxError: {e}")
    print(f"   Linea {e.lineno}: {e.text}")

print("\n📊 METRICHE:")
print(f"   - Fidelity: {result.fidelity_score:.3f}")
print(f"   - Compressione: {result.encoding_result.compression_result.compression_ratio:.1f}%")
print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
