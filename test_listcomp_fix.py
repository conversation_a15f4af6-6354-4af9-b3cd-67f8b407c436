#!/usr/bin/env python3

from neuroglyph.core.encoder.encoder import NGEncoder

# Test minimale per list comprehension fix
code = """
def process_data():
    filtered = [x for x in items if x > 0]
    return [x**2 for x in filtered]
""".strip()

print("🔍 TEST LIST COMPREHENSION FIX")
print("=" * 50)

try:
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=3)
    
    print("\n📝 CODICE ORIGINALE:")
    print(code)
    
    print("\n🔧 SIMBOLI GENERATI:")
    print(result.encoding_result.compressed_symbols)
    
    print("\n🔄 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    
    print("\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity:.3f}")
    print(f"   - Compressione: {result.encoding_result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - <PERSON><PERSON><PERSON> valida: {'✅' if result.decoding_result.syntax_valid else '❌'}")
    
except Exception as e:
    print(f"❌ ERRORE: {e}")
    import traceback
    traceback.print_exc()
